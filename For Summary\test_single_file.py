#!/usr/bin/env python3
"""
Simple test script to debug a single SRT file processing
"""

import os
from google import genai
from google.genai import types

# Configuration
API_KEY = "AIzaSyA2BF1yRzYQrkD_aqxk1dBjm_THcsAecLU"
MODEL_NAME = "gemini-2.5-pro-preview-05-06"

# Simple Hindi prompt for testing
SIMPLE_HINDI_PROMPT = """आपको एक कक्षा सत्र का ट्रांसक्रिप्ट दिया गया है। कृपया एक HTML सारांश तैयार करें।

महत्वपूर्ण: आपका उत्तर केवल HTML में होना चाहिए।

HTML संरचना:
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>कक्षा सत्र सारांश</title>
</head>
<body>
<p>प्रिय विद्यार्थी, नमस्कार!</p>
<h1>मुख्य विषय</h1>
<p>सत्र का संक्षिप्त विवरण</p>
<h2>मुख्य बिंदु</h2>
<ul>
<li>पहला मुख्य बिंदु</li>
<li>दूसरा मुख्य बिंदु</li>
</ul>
</body>
</html>

कृपया इसी प्रारूप में सारांश तैयार करें।"""

def extract_transcript(srt_file):
    """Extract text from SRT file"""
    with open(srt_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    return ' '.join([line.strip() for line in lines if line.strip() and not line.strip().isdigit() and '-->' not in line])

def test_single_file(srt_path):
    """Test processing a single SRT file"""
    print(f"🧪 Testing file: {srt_path}")

    # Extract transcript
    transcript = extract_transcript(srt_path)
    print(f"📄 Transcript length: {len(transcript)} characters")
    print(f"📄 Transcript preview: {transcript[:200]}...")

    # Limit transcript for testing
    test_transcript = transcript[:2000]  # First 2000 chars for testing

    print(f"\n🚀 Sending request to Gemini API...")

    try:
        # Create client with new SDK
        client = genai.Client(api_key=API_KEY)

        # Prepare contents for the new API format
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(text=test_transcript),
                ],
            ),
        ]

        # Generate content configuration
        generate_content_config = types.GenerateContentConfig(
            temperature=0.7,
            response_mime_type="text/plain",
            system_instruction=[
                types.Part.from_text(text=SIMPLE_HINDI_PROMPT),
            ],
        )

        # Generate the summary using streaming
        response_text = ""
        for chunk in client.models.generate_content_stream(
            model=MODEL_NAME,
            contents=contents,
            config=generate_content_config,
        ):
            if hasattr(chunk, 'text') and chunk.text:
                response_text += chunk.text

        print(f"✅ Response received!")
        print(f"📝 Response length: {len(response_text)} characters")
        print(f"📝 Response preview: {response_text[:300]}...")

        # Save full response for analysis
        output_file = srt_path.replace('.srt', '_test_response.html')
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(response_text)
        print(f"💾 Full response saved to: {output_file}")

        # Check for HTML structure
        if "<!DOCTYPE html>" in response_text:
            print("✅ Found DOCTYPE declaration")
        elif "<html>" in response_text:
            print("⚠️ Found HTML tag but no DOCTYPE")
        else:
            print("❌ No HTML structure found")

        if "</html>" in response_text:
            print("✅ Found closing HTML tag")
        else:
            print("❌ No closing HTML tag found")

    except Exception as e:
        print(f"❌ API Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Test with a specific file
    test_file = input("Enter path to a single .srt file to test: ").strip()

    if os.path.exists(test_file) and test_file.endswith('.srt'):
        test_single_file(test_file)
    else:
        print("❌ Invalid file path or not an SRT file")
