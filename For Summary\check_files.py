#!/usr/bin/env python3
"""
Simple script to check CSV and SQL files in the SRT folder
"""

import csv
import os
import glob

def check_files_in_folder(folder_path):
    """Check CSV and SQL files in the specified folder"""
    print(f"🔍 Checking files in: {folder_path}")
    
    # Check for CSV files
    csv_pattern = os.path.join(folder_path, "summaries_*.csv")
    csv_files = glob.glob(csv_pattern)
    
    print(f"\n📄 CSV Files found: {len(csv_files)}")
    for csv_file in csv_files:
        print(f"  - {os.path.basename(csv_file)}")
        
        # Check content
        try:
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                count = sum(1 for row in reader)
                print(f"    📊 Rows: {count}")
                
            # Check first row for encoding
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                first_row = next(reader, None)
                if first_row and 'summary_text' in first_row:
                    summary = first_row['summary_text']
                    if any(char in summary for char in ['क', 'ख', 'ग', 'च', 'ज']):
                        print("    ✅ Contains proper Hindi characters")
                    elif 'à¤' in summary:
                        print("    ⚠️ Contains encoded characters (needs fixing)")
                    else:
                        print("    ❓ No Hindi characters detected")
                        
        except Exception as e:
            print(f"    ❌ Error reading CSV: {e}")
    
    # Check for SQL files
    sql_pattern = os.path.join(folder_path, "summaries_*.sql")
    sql_files = glob.glob(sql_pattern)
    
    print(f"\n📄 SQL Files found: {len(sql_files)}")
    for sql_file in sql_files:
        print(f"  - {os.path.basename(sql_file)}")
        
        try:
            with open(sql_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Count INSERT statements
            insert_count = content.count('INSERT INTO')
            print(f"    📊 INSERT statements: {insert_count}")
            
            # Check encoding
            if 'à¤' in content:
                print("    ⚠️ Contains encoded characters (needs fixing)")
            elif any(char in content for char in ['क', 'ख', 'ग', 'च', 'ज']):
                print("    ✅ Contains proper Hindi characters")
            else:
                print("    ❓ No Hindi characters detected")
                
        except Exception as e:
            print(f"    ❌ Error reading SQL: {e}")

if __name__ == "__main__":
    print("📁 File Checker Tool")
    print("=" * 30)
    
    # Default to the SRT folder path from your previous run
    default_path = "/mnt/d/Course Transcripts/15977/srt"
    
    folder_path = input(f"📂 Enter folder path (default: {default_path}): ").strip()
    
    if not folder_path:
        folder_path = default_path
    
    if os.path.exists(folder_path):
        check_files_in_folder(folder_path)
    else:
        print(f"❌ Folder not found: {folder_path}")
        
    print("\n🎉 File check completed!")
