#!/usr/bin/env python3
"""
Gemini AI Video Summary Generator
Generates HTML summaries from SRT files in Hindi and English
"""

import os
import time
import csv
import traceback
import threading
import concurrent.futures
import google.generativeai as genai
import re

# ======== Configuration ========
API_KEY = "AIzaSyA2BF1yRzYQrkD_aqxk1dBjm_THcsAecLU"
MODEL_NAME = "gemini-2.5-pro-preview-05-06"
REQUESTS_PER_MIN = 5
MAX_WORKERS = 5
MAX_RETRIES = 5

# Generate unique filename with timestamp
timestamp = time.strftime("%Y%m%d_%H%M%S")
CSV_HEADER = ['video_id', 'summary_text', 'language']

# ======== Prompts ========
ENGLISH_PROMPT = """You have a class session transcript. Generate a comprehensive HTML summary to inspire students to watch/read the entire session, following these precise requirements:

## 1. Technical HTML Structure
1. Begin exactly with:
   <!DOCTYPE html>
   <html>
   <head>
   <meta charset="UTF-8">
   <title>Class Session Summary: [Insert Topic Name]</title>
   </head>
   <body>
2. End exactly with:
   </body>
   </html>
3. Use only valid HTML tags: <h1>, <h2>, <h3>, <p>, <strong>, <em>, <ul>, <li>, <ol>, <div>, <span>, <br>, <hr>.
4. No CSS, JavaScript, or inline styling.
5. Create semantic document structure with appropriate heading hierarchy.

## 2. Greeting & Introduction
1. The very first lines inside the <body> **must** be:
   <p>Dear Student, Greetings!</p>
2. Immediately after, include a compelling <p> paragraph (50-75 words) explaining:
   - The session's value for exams and intellectual growth
   - How it connects to broader academic concepts
   - What specific skills/knowledge students will gain

## 3. Required Sections Structure
Create these sections in the following order, each with appropriate heading levels:
1. **Title & Brief Description**
   - <h1> for the title
   - Include subject area, topic name, and a 2-3 sentence summary

2. **Session Overview (Key Takeaways)**
   - <h2> for the heading
   - 5-7 bullet points capturing the most crucial concepts
   - Each point should be 1-2 sentences maximum

3. **Section-Wise Breakdown with Timestamps**
   - <h2> for the heading
   - IMPORTANT: Consolidate timestamps into 7-8 MAXIMUM major sections
   - Format exactly as specified below in section 5

4. **Key Concepts & Terminology**
   - <h2> for the heading
   - Each concept gets its own <h3> subheading
   - 75-100 words of explanation per concept
   - Include examples from the transcript
   - Use <strong> for key terms within explanations

5. **Practical Insights & Highlights**
   - <h2> for the heading
   - Focus on exam relevance and real-world applications
   - 3-5 bullet points with practical takeaways

6. **Conclusion & Next Class Preview**
   - <h2> for the heading
   - Summarize the session's importance (50-75 words)
   - Include preview of next class if mentioned
   - End with a motivational statement encouraging full engagement

## 4. Content Accuracy Guidelines
1. Maintain 100% factual accuracy to the transcript.
2. Correct only clear typographical errors.
3. Preserve all technical terms, proper nouns, and specialized vocabulary exactly as written.
4. Do not invent content, examples, or explanations not present in the transcript.
5. If information is ambiguous, note this with "(as mentioned in the transcript)" rather than guessing.

## 5. Timestamp Section Requirements
1. IMPORTANT: Consolidate the timestamps into 7-8 MAXIMUM major sections, even if the transcript contains many more timestamps.
2. When consolidating:
   - Prioritize major topic shifts and significant content sections
   - Merge consecutive short timestamps that cover related topics
   - Ensure the sections cover the entire lecture from beginning to end
   - Select timestamps that represent substantial chunks of content
   - Include the very first and very last timestamps to cover the full range
3. Format each timestamp section exactly as:
   <div class="timestamp-section">
     <p><strong>Time:</strong> HH:MM:SS – HH:MM:SS</p>
     <p><strong>Topic:</strong> [Major Topic Name for This Consolidated Section]</p>
     <ul>
       [4-8 detailed bullet points summarizing key content from this time segment]
     </ul>
   </div>
4. For each timestamp section:
   - Use the exact time format from the transcript (00:00:00)
   - Provide a concise, descriptive topic that captures the main theme of the section
   - Create 4-8 detailed bullet points that thoroughly explain the most important information
   - Each bullet point should be substantive (2-3 sentences) with specific examples or explanations
   - Include definitions of key terms introduced during this segment
   - Highlight significant examples, case studies, or instructor insights
   - Include any relevant sub-points or elaborations to provide comprehensive coverage

Remember: Your final summary must inspire students to engage with the full lecture material while providing a comprehensive and accurate overview of the content. Quality and accuracy are paramount."""

HINDI_PROMPT = """आपको एक कक्षा सत्र का ट्रांसक्रिप्ट दिया गया है। कृपया एक व्यापक HTML सारांश तैयार करें जो छात्रों को पूरा सत्र देखने/पढ़ने के लिए प्रेरित करे।

महत्वपूर्ण: आपका उत्तर केवल HTML में होना चाहिए, कोई अन्य टेक्स्ट नहीं।

तकनीकी HTML संरचना:
अपना आउटपुट बिल्कुल इस प्रारूप में शुरू करें:
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>कक्षा सत्र सारांश: [विषय का नाम डालें]</title>
</head>
<body>

और बिल्कुल इस प्रकार समाप्त करें:
</body>
</html>

अनुमोदित HTML टैग:
केवल निम्नलिखित टैग का प्रयोग करें:
<h1>, <h2>, <h3>, <p>, <strong>, <em>, <ul>, <li>, <ol>, <div>, <span>, <br>, <hr>
कोई CSS, JavaScript, या इनलाइन स्टाइलिंग न जोड़ें।
HTML टैग अनुक्रम का सही उपयोग करें ताकि सारांश स्पष्ट, संरचित और पढ़ने में आसान हो।

1. अभिवादन और परिचय
<body> टैग के अंदर सबसे पहले यह पंक्ति अवश्य हो:
<p>प्रिय विद्यार्थी,
नमस्कार!</p>
इसके तुरंत बाद, एक संक्षिप्त और प्रेरणादायक परिचयात्मक अनुच्छेद (<p>, 50-75 शब्द) जिसमें शामिल हो:
सत्र की परीक्षाओं एवं बौद्धिक विकास में भूमिका।
यह अकादमिक अवधारणाओं से कैसे जुड़ा है।
छात्र कौन-से महत्वपूर्ण कौशल और ज्ञान प्राप्त करेंगे।

2. आवश्यक खंड संरचना
नीचे दिए गए सटीक क्रम में HTML शीर्षकों का उपयोग करें:
2.1. शीर्षक और संक्षिप्त विवरण
<h1> टैग के तहत:
विषय क्षेत्र, विषय का नाम, और मुख्य विषय-वस्तु का 2-3 वाक्य में सारांश।
2.2. सत्र अवलोकन (प्रमुख निष्कर्ष)
<h2> टैग के तहत:
केवल शैक्षणिक सामग्री से 6-8 प्रमुख अवधारणाओं का सार।
प्रत्येक बिंदु 1-2 वाक्य का हो, और UPSC/सरकारी परीक्षा की औपचारिक भाषा का प्रयोग हो।

3. समय अनुसार खंड-वार विभाजन (समय-निर्धारित खंड)
महत्वपूर्ण सुधार:
12-15 खंडों तक विस्तारित करें, जिससे संपूर्ण शैक्षणिक सामग्री का अधिक व्यापक कवरेज सुनिश्चित हो।
प्रत्येक समय-खंड को समेकित करें ताकि पूरा सत्र समान रूप से प्रस्तुत हो।
अनौपचारिक बातचीत, व्यक्तिगत संवाद, प्रशासनिक चर्चाएँ और अन्य गैर-शैक्षणिक सामग्री हटाएँ।
प्रत्येक समय-खंड का प्रारूप:
<div class="timestamp-section">
   <p><strong>Time:</strong> HH:MM:SS – HH:MM:SS</p>
  <p><strong>विषय:</strong> [इस खंड के लिए प्रमुख विषय]</p>
  <ul>
    [4-8 विस्तृत बुलेट पॉइंट, जो उस समय अवधि के सभी महत्वपूर्ण शैक्षणिक बिंदुओं को शामिल करें]
  </ul>
</div>
समय-खंड चयन के दिशा-निर्देश:
समय सटीक (HH:MM:SS) प्रारूप में दें।
प्रत्येक खंड 5-10 मिनट की अवधि का हो, जिससे पूरी रिकॉर्डिंग का निष्पक्ष कवरेज हो।
UPSC/सरकारी परीक्षा के तकनीकी शब्दावली और औपचारिक भाषा का प्रयोग करें।
प्रत्येक बुलेट बिंदु 2-3 वाक्य में संक्षिप्त और प्रभावी हो।
(अभिलेख में उल्लिखित अनुसार) नोट करें यदि जानकारी स्पष्ट नहीं है, कोई अनुमान न लगाएँ।

4. प्रमुख अवधारणाएँ और शब्दावली
<h2> टैग के तहत:
4-6 प्रमुख अवधारणाओं का चयन।
प्रत्येक अवधारणा का <h3> उपशीर्षक हो।
75-100 शब्दों में स्पष्ट व्याख्या दें, और महत्वपूर्ण शब्दों को <strong> में रखकर उभारें।
संस्कृत/शास्त्रीय हिंदी शब्दों को मूल रूप में बनाए रखें, आवश्यकतानुसार आधुनिक हिंदी व्याख्या दें।

5. व्यावहारिक अंतर्दृष्टि और परीक्षा प्रासंगिकता
<h2> टैग के तहत:
परीक्षा प्रासंगिकता और वास्तविक अनुप्रयोगों पर केंद्रित 3-5 बिंदु (बुलेट पॉइंट)।
UPSC/सरकारी परीक्षाओं के अनुरूप सटीकता और औपचारिकता बनाए रखें।

6. निष्कर्ष और अगली कक्षा पूर्वावलोकन
<h2> टैग के तहत:
सत्र के महत्व का 50-75 शब्दों का संक्षिप्त सारांश।
यदि अगली कक्षा का उल्लेख है, तो उसका पूर्वावलोकन दें।
प्रेरणादायक निष्कर्ष दें, जिससे छात्र संपूर्ण सामग्री से जुड़े रहें।"""

# Language templates
LANGUAGES = {
    "english": {"name": "English", "prompt": ENGLISH_PROMPT},
    "hindi": {"name": "Hindi", "prompt": HINDI_PROMPT}
}

# ======== Rate Limiting ========
class RateLimiter:
    def __init__(self, max_requests_per_minute):
        self.max_requests = max_requests_per_minute
        self.request_timestamps = []
        self.lock = threading.Lock()

    def wait_if_needed(self):
        now = time.time()
        with self.lock:
            # Remove old timestamps
            self.request_timestamps = [ts for ts in self.request_timestamps if now - ts < 60]

            # Wait if needed
            if len(self.request_timestamps) >= self.max_requests:
                oldest = min(self.request_timestamps)
                sleep_time = max(0, 60 - (now - oldest))
                if sleep_time > 0:
                    print(f"⏱️ Rate limit reached. Waiting {sleep_time:.1f} seconds...")
                    time.sleep(sleep_time)

            # Add current timestamp
            self.request_timestamps.append(time.time())

rate_limiter = RateLimiter(REQUESTS_PER_MIN)

# ======== Utility Functions ========
def extract_transcript(srt_file):
    """Extract text from SRT file"""
    try:
        with open(srt_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # Remove timestamps, line numbers, and empty lines
        text_lines = []
        for line in lines:
            line = line.strip()
            if line and not line.isdigit() and '-->' not in line:
                text_lines.append(line)

        return ' '.join(text_lines)
    except Exception as e:
        print(f"❌ Error reading SRT file {srt_file}: {e}")
        return None

def extract_html_from_response(response_text):
    """Extract HTML content from API response"""
    if not response_text:
        return None

    # Method 1: Look for HTML in markdown code blocks
    html_pattern = r"```html\s*(<!DOCTYPE html>.*?</html>)\s*```"
    match = re.search(html_pattern, response_text, re.DOTALL | re.IGNORECASE)
    if match:
        return match.group(1).strip()

    # Method 2: Look for direct HTML
    html_pattern = r"<!DOCTYPE html>.*?</html>"
    match = re.search(html_pattern, response_text, re.DOTALL | re.IGNORECASE)
    if match:
        return match.group(0).strip()

    # Method 3: Look for HTML without DOCTYPE
    html_pattern = r"<html>.*?</html>"
    match = re.search(html_pattern, response_text, re.DOTALL | re.IGNORECASE)
    if match:
        html_content = match.group(0).strip()
        return f"<!DOCTYPE html>\n{html_content}"

    # Method 4: Check if it's mostly HTML content
    if len(response_text) > 500 and response_text.count('<') > 10:
        if any(tag in response_text.lower() for tag in ['<body>', '<h1>', '<h2>', '<p>']):
            # Wrap in basic HTML structure
            return f"""<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>कक्षा सत्र सारांश</title>
</head>
<body>
{response_text.strip()}
</body>
</html>"""

    return None

def validate_html_content(html_content):
    """Validate HTML content quality"""
    if not html_content or len(html_content) < 500:
        return False

    # Check for basic HTML structure
    has_doctype = "<!DOCTYPE html>" in html_content or "<html>" in html_content
    has_closing = "</html>" in html_content
    has_content = any(tag in html_content for tag in ["<h1>", "<h2>", "<p>", "<ul>"])

    return has_doctype and has_closing and has_content

# ======== Main Processing Functions ========
def generate_summary(transcript, language):
    """Generate summary using Gemini API"""
    try:
        rate_limiter.wait_if_needed()

        # Configure API
        genai.configure(api_key=API_KEY)

        # Get prompt
        if language not in LANGUAGES:
            language = "hindi"

        system_prompt = LANGUAGES[language]["prompt"]

        # Create model with system instruction
        model = genai.GenerativeModel(
            model_name=MODEL_NAME,
            system_instruction=system_prompt
        )

        print(f"🚀 Sending request to Gemini API...")

        # Generate content
        response = model.generate_content(
            transcript,
            generation_config=genai.types.GenerationConfig(
                temperature=0.7,
                top_p=0.8,
                top_k=40,
                max_output_tokens=8192,
            )
        )

        # Extract response text
        try:
            if hasattr(response, 'text') and response.text:
                response_text = response.text
                print(f"✅ Received response: {len(response_text)} characters")
            else:
                print("⚠️ No text in response")
                return None
        except ValueError as e:
            if "finish_reason" in str(e):
                print(f"⚠️ Response blocked by safety filter: {e}")
                return None
            else:
                print(f"⚠️ Error accessing response: {e}")
                return None

        # Extract HTML
        html_content = extract_html_from_response(response_text)

        if html_content and validate_html_content(html_content):
            return html_content
        else:
            print(f"⚠️ Invalid HTML content generated")
            return None

    except Exception as e:
        print(f"❌ Error generating summary: {e}")
        traceback.print_exc()
        return None

def process_single_file(file_path, language, max_retries=MAX_RETRIES):
    """Process a single SRT file with retries"""
    video_id = os.path.splitext(os.path.basename(file_path))[0]
    print(f"\n📝 Processing: {video_id} in {language}")

    # Extract transcript
    transcript = extract_transcript(file_path)
    if not transcript:
        print(f"❌ Failed to extract transcript from {file_path}")
        return None

    # Try to generate summary with retries
    for attempt in range(max_retries):
        print(f"🔄 Attempt {attempt + 1}/{max_retries} for {video_id}")

        summary = generate_summary(transcript, language)

        if summary:
            print(f"✅ Successfully generated summary for {video_id}")
            return {
                'video_id': video_id,
                'summary_text': summary,
                'language': language
            }
        else:
            if attempt < max_retries - 1:
                wait_time = min(30, 5 * (2 ** attempt))  # Exponential backoff, max 30s
                print(f"⏳ Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)

    print(f"❌ All {max_retries} attempts failed for {video_id}")
    return None

def load_existing_summaries(csv_file):
    """Load already processed video IDs"""
    if not os.path.exists(csv_file):
        return set()

    try:
        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            return set(row['video_id'] for row in reader if 'video_id' in row)
    except Exception as e:
        print(f"⚠️ Error loading existing summaries: {e}")
        return set()

def save_summary_to_csv(summary_data, csv_file):
    """Save summary to CSV file"""
    file_exists = os.path.exists(csv_file)

    try:
        with open(csv_file, 'a', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=CSV_HEADER)

            if not file_exists:
                writer.writeheader()

            writer.writerow(summary_data)
        return True
    except Exception as e:
        print(f"❌ Error saving to CSV: {e}")
        return False

def process_folder(folder_path, language):
    """Process all SRT files in a folder"""
    print(f"\n🚀 Processing folder: {folder_path}")
    print(f"🌐 Language: {LANGUAGES[language]['name']}")

    # Find SRT files
    srt_files = [f for f in os.listdir(folder_path) if f.endswith('.srt')]

    if not srt_files:
        print("❌ No SRT files found in the folder")
        return

    print(f"📁 Found {len(srt_files)} SRT files")

    # Create output CSV file
    csv_filename = f"summaries_{language}_{timestamp}.csv"
    csv_path = os.path.join(folder_path, csv_filename)

    # Load existing summaries
    existing_ids = load_existing_summaries(csv_path)

    # Filter files to process
    files_to_process = []
    for srt_file in srt_files:
        video_id = os.path.splitext(srt_file)[0]
        if video_id not in existing_ids:
            files_to_process.append(srt_file)

    if not files_to_process:
        print("✅ All files have already been processed")
        return

    print(f"📝 Processing {len(files_to_process)} new files...")

    # Process files with threading
    successful = 0
    failed = 0
    csv_lock = threading.Lock()

    def process_and_save(srt_file):
        nonlocal successful, failed

        file_path = os.path.join(folder_path, srt_file)
        result = process_single_file(file_path, language)

        if result:
            with csv_lock:
                if save_summary_to_csv(result, csv_path):
                    successful += 1
                    print(f"✅ Saved: {result['video_id']}")
                else:
                    failed += 1
        else:
            failed += 1

    # Use ThreadPoolExecutor for concurrent processing
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = [executor.submit(process_and_save, srt_file) for srt_file in files_to_process]

        # Wait for completion
        for future in concurrent.futures.as_completed(futures):
            try:
                future.result()
            except Exception as e:
                print(f"❌ Unexpected error: {e}")
                failed += 1

    # Print results
    print(f"\n🎉 Processing completed!")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📄 Output file: {csv_path}")

    # Offer CSV to SQL conversion
    if successful > 0:
        convert = input("\n🔄 Convert CSV to SQL format? (y/n): ").strip().lower()
        if convert in ['y', 'yes']:
            convert_csv_to_sql(csv_path)

def convert_csv_to_sql(csv_file):
    """Convert CSV to SQL INSERT statements"""
    try:
        sql_file = csv_file.replace('.csv', '.sql')

        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)

            with open(sql_file, 'w', encoding='utf-8') as sql_f:
                sql_f.write("-- SQL statements for importing data to dla_ai_video_summary table\n")
                sql_f.write("START TRANSACTION;\n\n")

                batch_size = 10
                batch = []
                batch_count = 0

                for row in reader:
                    video_id = row['video_id']
                    summary_text = row['summary_text'].replace("'", "''")  # Escape quotes

                    batch.append(f"({video_id}, '{summary_text}')")

                    if len(batch) >= batch_size:
                        batch_count += 1
                        sql_f.write(f"-- Batch {batch_count}\n")
                        sql_f.write("INSERT INTO dla_ai_video_summary (video_id, summary_text) VALUES\n")
                        sql_f.write(",\n".join(batch))
                        sql_f.write(";\n\n")
                        batch = []

                # Write remaining batch
                if batch:
                    batch_count += 1
                    sql_f.write(f"-- Batch {batch_count}\n")
                    sql_f.write("INSERT INTO dla_ai_video_summary (video_id, summary_text) VALUES\n")
                    sql_f.write(",\n".join(batch))
                    sql_f.write(";\n\n")

                # Add timestamp updates
                sql_f.write("-- Update timestamps\n")
                sql_f.write("UPDATE dla_ai_video_summary SET created_at = NOW(), updated_at = NOW() ")
                sql_f.write("WHERE created_at IS NULL;\n\n")
                sql_f.write("COMMIT;\n")

        print(f"✅ SQL file created: {sql_file}")

    except Exception as e:
        print(f"❌ Error creating SQL file: {e}")

def main():
    """Main function"""
    print("🎓 Gemini AI Video Summary Generator")
    print("=" * 50)

    # Get folder path
    folder_path = input("📂 Enter path to folder containing .srt files: ").strip()

    if not os.path.isdir(folder_path):
        print("❌ Invalid folder path")
        return

    # Get language
    print("\n🌐 Available languages:")
    print("1. Hindi (हिंदी)")
    print("2. English")

    while True:
        choice = input("\n🔤 Select language (1 for Hindi, 2 for English): ").strip()

        if choice == "1" or choice.lower() in ["hindi", "hin", "h"]:
            language = "hindi"
            break
        elif choice == "2" or choice.lower() in ["english", "eng", "e"]:
            language = "english"
            break
        else:
            print("⚠️ Invalid choice. Please enter 1 or 2.")

    print(f"✅ Selected: {LANGUAGES[language]['name']}")

    # Start processing
    process_folder(folder_path, language)

if __name__ == "__main__":
    main()
