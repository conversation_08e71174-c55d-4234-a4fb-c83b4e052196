import csv
import sys
import argparse

def escape_sql_string(s):
    """Escape single quotes in a string for SQL."""
    if s is None:
        return "NULL"
    return s.replace("'", "''")

def csv_to_sql(csv_file, sql_file, update_timestamps=True, user_id=1):
    """
    Convert CSV file to SQL INSERT statements and optionally include UPDATE statements 
    for timestamp columns and user IDs.
    """
    try:
        with open(csv_file, 'r', encoding='utf-8') as csv_f:
            # Read CSV file
            reader = csv.DictReader(csv_f)
            
            # Store all video_ids for the timestamp update
            all_video_ids = []
            
            # Create SQL file
            with open(sql_file, 'w', encoding='utf-8') as sql_f:
                # Write header
                sql_f.write("-- SQL statements for importing data to dla_ai_video_summary table\n")
                sql_f.write("START TRANSACTION;\n\n")
                
                # Process rows in batches of 10
                batch_size = 10
                current_batch = []
                batch_counter = 0
                
                for row in reader:
                    if 'video_id' in row and 'summary_text' in row:
                        video_id = row['video_id'].strip()
                        summary_text = escape_sql_string(row['summary_text'])
                        
                        # Store video_id for timestamp update
                        all_video_ids.append(video_id)
                        
                        # Add to current batch
                        current_batch.append(f"({video_id}, '{summary_text}')")
                        
                        # Write batch when it reaches size limit
                        if len(current_batch) >= batch_size:
                            batch_counter += 1
                            sql_f.write(f"-- Batch {batch_counter}\n")
                            sql_f.write("INSERT INTO dla_ai_video_summary (video_id, summary_text) VALUES\n")
                            sql_f.write(",\n".join(current_batch))
                            sql_f.write(";\n\n")
                            current_batch = []
                
                # Write any remaining rows
                if current_batch:
                    batch_counter += 1
                    sql_f.write(f"-- Batch {batch_counter}\n")
                    sql_f.write("INSERT INTO dla_ai_video_summary (video_id, summary_text) VALUES\n")
                    sql_f.write(",\n".join(current_batch))
                    sql_f.write(";\n\n")
                
                # Add timestamp and user ID update if requested
                if update_timestamps and all_video_ids:
                    sql_f.write("-- Update timestamp columns and user IDs\n")
                    sql_f.write(f"""
UPDATE dla_ai_video_summary 
SET 
  created_at = NOW(),
  updated_at = NOW(),
  created_by = {user_id},
  updated_by = {user_id}
WHERE 
  video_id IN ({', '.join(all_video_ids)});
""")
                    sql_f.write("\n")
                
                # Commit the transaction
                sql_f.write("COMMIT;\n")
                
        print(f"Successfully converted {csv_file} to {sql_file}")
        print(f"Generated {batch_counter} INSERT batches")
        if update_timestamps:
            print(f"Added timestamp UPDATE statement for {len(all_video_ids)} rows")
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    # Set up command line arguments
    parser = argparse.ArgumentParser(description='Convert CSV file to SQL statements')
    parser.add_argument('csv_file', help='Input CSV file path')
    parser.add_argument('sql_file', help='Output SQL file path')
    parser.add_argument('--no-timestamps', action='store_true', 
                        help='Skip adding timestamp updates')
    parser.add_argument('--user-id', type=int, default=1,
                        help='User ID for created_by and updated_by columns (default: 1)')
    
    args = parser.parse_args()
    
    # Run the conversion
    csv_to_sql(
        args.csv_file, 
        args.sql_file, 
        update_timestamps=not args.no_timestamps,
        user_id=args.user_id
    )