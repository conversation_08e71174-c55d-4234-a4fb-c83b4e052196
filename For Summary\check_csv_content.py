#!/usr/bin/env python3
"""
Quick script to check the content of the generated CSV file
"""

import csv
import os
import glob

def check_latest_csv():
    """Find and check the latest CSV file"""
    # Find all CSV files starting with 'summaries_'
    csv_files = glob.glob("summaries_*.csv")
    
    if not csv_files:
        print("❌ No CSV files found")
        return
    
    # Get the latest file
    latest_csv = max(csv_files, key=os.path.getctime)
    print(f"📄 Checking latest CSV: {latest_csv}")
    
    try:
        with open(latest_csv, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            count = 0
            for row in reader:
                count += 1
                video_id = row.get('video_id', 'Unknown')
                summary = row.get('summary_text', '')
                language = row.get('language', 'Unknown')
                
                print(f"\n📝 Entry {count}: {video_id} ({language})")
                print(f"📏 Summary length: {len(summary)} characters")
                
                # Check for HTML structure
                if "<!DOCTYPE html>" in summary:
                    print("✅ Has DOCTYPE")
                elif "<html>" in summary:
                    print("⚠️ Has HTML tag but no DOCTYPE")
                else:
                    print("❌ No HTML structure")
                
                # Check for Hindi content
                if any(char in summary for char in ['क', 'ख', 'ग', 'च', 'ज', 'त', 'द', 'न', 'प', 'म', 'य', 'र', 'ल', 'व', 'श', 'स', 'ह']):
                    print("✅ Contains proper Hindi Devanagari characters")
                elif 'à¤' in summary:
                    print("⚠️ Contains encoded Hindi characters (display issue)")
                else:
                    print("❓ No Hindi characters detected")
                
                # Show first 200 characters
                print(f"📖 Preview: {summary[:200]}...")
                
                if count >= 3:  # Show first 3 entries
                    break
            
            print(f"\n📊 Total entries found: {count}")
            
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")

if __name__ == "__main__":
    check_latest_csv()
