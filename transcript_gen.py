import os
import sys
import json
import logging
import time
from pathlib import Path
import subprocess
import threading
from queue import Queue
import concurrent.futures
from datetime import timedelta, datetime
import pickle

import torch
from pydub import AudioSegment
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler("transcription_pipeline.log"),
        logging.StreamHandler()
    ]
)

COOLING_PERIOD_SECONDS = 120  # 2 minutes

class ProcessingState:
    def __init__(self, state_file="processing_state.pkl"):
        self.state_file = Path(state_file)
        self.processed_files = set()
        self.in_progress_files = set()
        self.load_state()

    def load_state(self):
        if self.state_file.exists():
            try:
                with open(self.state_file, 'rb') as f:
                    state_data = pickle.load(f)
                    if isinstance(state_data, dict):
                        self.processed_files = state_data.get('processed', set())
                        self.in_progress_files = state_data.get('in_progress', set())
                    else:
                        self.processed_files = state_data
                        self.in_progress_files = set()
            except Exception as e:
                if self.state_file.exists():
                    import shutil
                    backup_name = f"processing_state_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
                    shutil.copy2(self.state_file, backup_name)
                self.processed_files = set()
                self.in_progress_files = set()

    def save_state(self):
        try:
            state_data = {
                'processed': self.processed_files,
                'in_progress': self.in_progress_files,
                'timestamp': datetime.now().isoformat()
            }
            with open(self.state_file, 'wb') as f:
                pickle.dump(state_data, f)
        except Exception as e:
            logging.error(f"Error saving state file: {e}")

    def mark_in_progress(self, file_path: str):
        self.in_progress_files.add(str(file_path))
        self.save_state()

    def mark_completed(self, file_path: str):
        file_path_str = str(file_path)
        self.processed_files.add(file_path_str)
        self.in_progress_files.discard(file_path_str)
        self.save_state()

    def is_processed(self, file_path: str) -> bool:
        return str(file_path) in self.processed_files

    def is_in_progress(self, file_path: str) -> bool:
        return str(file_path) in self.in_progress_files

class GPUManager:
    def __init__(self):
        self.gpu_queue = Queue()
        self.init_available_gpus()

    def init_available_gpus(self):
        try:
            result = subprocess.run(['nvidia-smi', '-L'], capture_output=True, text=True)
            gpu_list = [line for line in result.stdout.strip().split('\n') if line]
            for i in range(len(gpu_list)):
                self.gpu_queue.put(i)
        except Exception as e:
            self.gpu_queue.put(0)

    def get_gpu(self):
        return self.gpu_queue.get()

    def release_gpu(self, gpu_id):
        self.gpu_queue.put(gpu_id)

def convert_to_wav(input_path: Path, output_path: Path) -> bool:
    try:
        audio = AudioSegment.from_file(input_path, format="m4a")
        audio.export(output_path, format="wav")
        print(f"Converted {input_path.name} to WAV")
        return True
    except Exception as e:
        logging.error(f"Error converting {input_path}: {str(e)}")
        return False

def process_audio_file(wav_path: Path, gpu_manager: GPUManager, output_json_path: Path, language: str, max_retries: int = 2, retry_delay: int = 30) -> bool:
    gpu_id = None
    attempt = 0
    while attempt <= max_retries:
        try:
            gpu_id = gpu_manager.get_gpu()
            env = os.environ.copy()
            env['CUDA_VISIBLE_DEVICES'] = str(gpu_id)

            cmd = [
                "insanely-fast-whisper",
                "--file-name", str(wav_path),
                "--device-id", "0",
                "--model-name", "openai/whisper-large-v3",
                "--transcript-path", str(output_json_path),
                "--timestamp", "chunk",
                "--batch-size", "24",
                "--language", language
            ]

            print(f"Attempt {attempt + 1}: Processing {wav_path.name} on GPU {gpu_id}")
            process = subprocess.run(cmd, env=env, check=True, capture_output=True, text=True, timeout=900)
            return process.returncode == 0

        except subprocess.TimeoutExpired:
            logging.warning(f"Timeout expired on attempt {attempt + 1} for {wav_path}")
        except subprocess.CalledProcessError as e:
            logging.warning(f"Command error on attempt {attempt + 1} for {wav_path}: {e.stderr}")
        except Exception as e:
            logging.error(f"Unexpected error on attempt {attempt + 1} for {wav_path}: {e}")
        finally:
            if gpu_id is not None:
                gpu_manager.release_gpu(gpu_id)

        attempt += 1
        if attempt <= max_retries:
            logging.info(f"Retrying {wav_path} in {retry_delay} seconds...")
            time.sleep(retry_delay)

    return False

def convert_json_to_srt(json_path: Path, srt_path: Path) -> bool:
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        with open(srt_path, 'w', encoding='utf-8') as f_out:
            for i, chunk in enumerate(data.get('chunks', []), 1):
                if not chunk.get('timestamp') or len(chunk['timestamp']) != 2:
                    continue
                f_out.write(f"{i}\n{format_timestamp(chunk['timestamp'][0])} --> {format_timestamp(chunk['timestamp'][1])}\n{chunk.get('text', '').strip()}\n\n")

        print(f"Converted {json_path.name} to SRT")
        return True
    except Exception as e:
        logging.error(f"Error converting {json_path} to SRT: {str(e)}")
        return False

def format_timestamp(seconds: float) -> str:
    td = timedelta(seconds=seconds)
    hours, remainder = divmod(td.seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

def process_single_file(args: tuple) -> (str, bool):
    m4a_file, output_base, gpu_manager, language, processing_state = args
    processing_state.mark_in_progress(m4a_file)

    wav_path = output_base / "wav" / f"{m4a_file.stem}.wav"
    json_path = output_base / "json" / f"{m4a_file.stem}.json"
    srt_path = output_base / "srt" / f"{m4a_file.stem}.srt"

    for d in [wav_path.parent, json_path.parent, srt_path.parent]:
        d.mkdir(parents=True, exist_ok=True)

    print(f"Processing file: {m4a_file.name}")
    if srt_path.exists() and json_path.exists():
        print(f"{m4a_file.name} already processed.")
        processing_state.mark_completed(m4a_file)
        return (m4a_file.name, True)

    if not wav_path.exists() and not convert_to_wav(m4a_file, wav_path):
        return (m4a_file.name, False)

    if not json_path.exists() and not process_audio_file(wav_path, gpu_manager, json_path, language):
        return (m4a_file.name, False)

    if wav_path.exists():
        try:
            wav_path.unlink()
        except Exception as e:
            logging.warning(f"Failed to delete {wav_path}: {e}")

    if not srt_path.exists() and not convert_json_to_srt(json_path, srt_path):
        return (m4a_file.name, False)

    processing_state.mark_completed(m4a_file)
    print(f"Finished processing {m4a_file.name}")
    time.sleep(COOLING_PERIOD_SECONDS)
    return (m4a_file.name, True)

def main():
    input_folder = input("Enter the folder path containing .m4a files: ").strip()
    language = input("Enter the language code for transcription (default: en): ").strip() or "en"
    max_workers = int(input("Enter the maximum number of concurrent workers (default: 4): ") or 4)
    resume_in_progress = input("Process files that were previously in progress? (Y/n): ").strip().lower() != 'n'

    input_path = Path(input_folder)
    gpu_manager = GPUManager()
    processing_state = ProcessingState()

    all_files = list(input_path.rglob("*.m4a"))
    in_progress_files = [f for f in all_files if processing_state.is_in_progress(f)]
    unprocessed_files = [f for f in all_files if not processing_state.is_processed(f) and not processing_state.is_in_progress(f)]

    if resume_in_progress:
        unprocessed_files += in_progress_files

    if not unprocessed_files:
        print("No new .m4a files to process.")
        return

    print(f"Total files to process: {len(unprocessed_files)}")

    failed_files = []
    with tqdm(total=len(unprocessed_files), desc="Processing files") as pbar:
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(process_single_file, (f, input_path, gpu_manager, language, processing_state)) for f in unprocessed_files]
            for future in concurrent.futures.as_completed(futures):
                file_name, success = future.result()
                if not success:
                    failed_files.append(file_name)
                pbar.update(1)

    print("\nAll processing complete.")
    if failed_files:
        print("\nThe following files failed to process:")
        for f in failed_files:
            print(f" - {f}")

        retry_input = input("\nWould you like to retry processing these files now? (Y/n): ").strip().lower()
        if retry_input != 'n':
            for f in failed_files:
                process_single_file((input_path / f, input_path, gpu_manager, language, processing_state))

if __name__ == '__main__':
    main()
