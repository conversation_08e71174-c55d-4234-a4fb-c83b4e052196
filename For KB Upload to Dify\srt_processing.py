import os
import re

def process_file(input_filepath, output_folder):
    """Processes a single .srt or .txt file, modifies timestamps, and saves as .txt."""
    filename = os.path.basename(input_filepath)
    file_prefix = os.path.splitext(filename)[0]  # Extract filename without extension (e.g., 7781)
    
    output_filepath = os.path.join(output_folder, f"{file_prefix}_modified.txt")

    try:
        with open(input_filepath, "r", encoding="utf-8") as infile:
            lines = infile.readlines()
    except Exception as e:
        print(f"❌ Error reading {filename}: {e}")
        return

    # Process the lines
    output_lines = []
    for line in lines:
        line = line.strip()
        if line.isdigit():  # Skip line numbers
            continue
        if "-->" in line:  # Timestamp line, remove milliseconds and add prefix
            clean_timestamp = re.sub(r",\d{3}", "", line)  # Remove milliseconds
            output_lines.append(f"{file_prefix}\n{clean_timestamp}")
        else:
            output_lines.append(line)

    if output_lines:
        os.makedirs(output_folder, exist_ok=True)
        try:
            with open(output_filepath, "w", encoding="utf-8") as outfile:
                outfile.write("\n".join(output_lines))
            print(f"✅ Processed: {filename} -> {output_filepath}")
        except Exception as e:
            print(f"❌ Error writing {filename}: {e}")
    else:
        print(f"⚠ Skipped empty or invalid file: {filename}")

def process_folder(folder_path):
    """Processes all .srt and .txt files in a given folder."""
    output_folder = os.path.join(folder_path, "processed_files")
    os.makedirs(output_folder, exist_ok=True)

    print(f"🔍 Checking folder: {folder_path}")
    files = os.listdir(folder_path)

    if not files:
        print("⚠ No files found in the specified folder.")
        return

    file_count = 0
    for file in files:
        input_filepath = os.path.join(folder_path, file)

        if os.path.isfile(input_filepath) and (file.endswith(".srt") or file.endswith(".txt")):
            print(f"📄 Found file: {file}")
            process_file(input_filepath, output_folder)
            file_count += 1

    if file_count == 0:
        print("⚠ No valid `.srt` or `.txt` files found in the folder.")
    else:
        print(f"\n🎉 {file_count} file(s) processed successfully! Saved in '{output_folder}'.")

# Get folder path from user input and process files
folder_path = input("Enter the folder path containing .srt and .txt files: ").strip()

if os.path.isdir(folder_path):
    process_folder(folder_path)
else:
    print("❌ Invalid folder path. Please check and try again.")
