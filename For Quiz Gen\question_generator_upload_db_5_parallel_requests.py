import os
import glob
import requests
import json
import logging
import time
import random
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor

# Configure logging to file and console
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("process_logs.log"),
        logging.StreamHandler()
    ]
)

# Function to read an .srt file and return its content as a list of lines
def read_srt_file(file_path):
    """Reads an .srt file and returns its content as a list of lines."""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.readlines()
    except Exception as e:
        logging.error(f"Error reading file {file_path}: {e}")
        return []

# Function to split transcript into equal parts while keeping timestamps
def split_into_parts(text_lines, num_parts=5):
    """Splits the text lines into equal parts while keeping timestamps."""
    if not text_lines:
        return [""] * num_parts  # Return empty parts if no text available
    
    total_lines = len(text_lines)
    part_size = max(1, total_lines // num_parts)  # Ensure at least 1 line per part
    parts = [text_lines[i * part_size: (i + 1) * part_size] for i in range(num_parts)]
    
    # If there are remaining lines, add them to the last part
    remaining = total_lines % num_parts
    if remaining:
        parts[-1].extend(text_lines[-remaining:])
    
    return ['\n'.join(part) for part in parts]

# Function to send transcript parts to the API for analysis with improved retry strategy
def analyze_transcript(video_id, video_part, transcript, language, api_key, analysis_url, max_retries=5):
    """
    Analyzes transcript part using Dify API with video_id and video_part.
    
    Implements an exponential backoff retry strategy to handle API failures.
    """
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }
    payload = {
        "query": "",
        "inputs": {"video_id": video_id, "video_part": video_part, "transcript": transcript, "language": language},
        "response_mode": "blocking",
        "conversation_id": "",
        "user": "abc-123",
    }
    
    # Initialize request ID to track this specific request
    request_id = f"{video_id}_{video_part}"
    
    # Exponential backoff retry logic
    for attempt in range(max_retries):
        try:
            logging.info(f"Attempt {attempt + 1}/{max_retries} for request {request_id}")
            response = requests.post(analysis_url, json=payload, headers=headers, timeout=30)
            
            # Check for HTTP errors
            response.raise_for_status()
            
            # Success - return the data
            logging.info(f"Request {request_id} successful on attempt {attempt + 1}")
            return response.json().get("data", {})
            
        except requests.exceptions.HTTPError as http_err:
            error_msg = f"HTTP error for {request_id} on attempt {attempt + 1}: {http_err}"
            logging.error(error_msg)
            
            # Handle different HTTP status codes differently
            if response.status_code == 429:  # Too Many Requests
                wait_time = int(response.headers.get('Retry-After', 60))
                logging.info(f"Rate limited. Waiting {wait_time} seconds before retry")
            else:
                # Calculate backoff time: 2^attempt + random jitter
                wait_time = (2 ** attempt) + random.uniform(0, 1)
                
            # Special case for server errors which might require longer backoff
            if 500 <= response.status_code < 600:
                wait_time = max(wait_time, 10)  # Wait at least 10 seconds for server errors
                
            logging.info(f"Waiting {wait_time:.2f} seconds before retrying...")
            time.sleep(wait_time)
            
        except requests.exceptions.ConnectionError as conn_err:
            error_msg = f"Connection error for {request_id} on attempt {attempt + 1}: {conn_err}"
            logging.error(error_msg)
            
            # Connection errors might need longer backoff
            wait_time = (2 ** attempt) + 5 + random.uniform(0, 1)
            logging.info(f"Waiting {wait_time:.2f} seconds before retrying...")
            time.sleep(wait_time)
            
        except requests.exceptions.Timeout as timeout_err:
            error_msg = f"Timeout for {request_id} on attempt {attempt + 1}: {timeout_err}"
            logging.error(error_msg)
            
            # For timeouts, we might want to increase the timeout for subsequent attempts
            wait_time = (2 ** attempt) + random.uniform(0, 1)
            logging.info(f"Waiting {wait_time:.2f} seconds before retrying...")
            time.sleep(wait_time)
            
        except Exception as e:
            error_msg = f"Unexpected error for {request_id} on attempt {attempt + 1}: {e}"
            logging.error(error_msg)
            
            # General exponential backoff for other exceptions
            wait_time = (2 ** attempt) + random.uniform(0, 1)
            logging.info(f"Waiting {wait_time:.2f} seconds before retrying...")
            time.sleep(wait_time)
    
    # If all retries fail, return error information
    error_msg = f"All {max_retries} retry attempts failed for request {request_id}"
    logging.error(error_msg)
    return {"error": error_msg}

# Functions to load and update the history file
def load_history(directory):
    """Loads processed video IDs from the history file in the directory."""
    history_file = os.path.join(directory, "processed_history.json")
    if os.path.exists(history_file):
        try:
            with open(history_file, 'r', encoding='utf-8') as f:
                processed_list = json.load(f)
        except Exception as e:
            logging.error(f"Error reading history file: {e}")
            processed_list = []
    else:
        processed_list = []
    return set(processed_list), processed_list, history_file

def update_history(video_id, processed_list, history_file):
    """Updates the history file by adding a new processed video_id."""
    if video_id not in processed_list:
        processed_list.append(video_id)
        try:
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(processed_list, f, indent=4)
            return True
        except Exception as e:
            logging.error(f"Error writing to history file: {e}")
            return False
    return False

# Function to process SRT files, analyze parts in parallel, and save results
def process_srt_files(directory, language, api_key, analysis_url):
    """
    Processes SRT files, splits them, sends parts to API in parallel (5 at a time), 
    and saves combined JSON. Skips files that have already been processed.
    """
    if not os.path.exists(directory) or not os.path.isdir(directory):
        raise FileNotFoundError(f"Directory does not exist: {directory}")
    
    srt_files = glob.glob(os.path.join(directory, '*.srt'))
    if not srt_files:
        raise FileNotFoundError(f"No .srt files found in directory: {directory}")
    
    processed_set, processed_list, history_file = load_history(directory)
    logging.info(f"Loaded history with {len(processed_set)} processed file(s).")
    
    # Dictionary to store futures per video_id
    all_futures = {}  # { video_id: [(part_name, future), ...] }
    
    # Use ThreadPoolExecutor to schedule API calls concurrently with a maximum of 5 workers
    with ThreadPoolExecutor(max_workers=5) as executor:
        for srt_file in tqdm(srt_files, desc="Scheduling transcripts"):
            video_id = os.path.basename(srt_file).replace('.srt', '')
            if video_id in processed_set:
                logging.info(f"Skipping {video_id} as it is already processed.")
                continue

            if not os.access(srt_file, os.R_OK):
                logging.error(f"Permission denied: {srt_file}")
                continue
            
            logging.info(f"Processing file: {srt_file}")
            lines = read_srt_file(srt_file)  # Read transcript
            parts = split_into_parts(lines)  # Split transcript into parts
            
            futures = []
            for i, part in enumerate(parts, 1):
                part_name = f"video_part_{i}_{video_id}"  # Name format: video_part_i_videoid
                logging.info(f"Scheduling analysis for {part_name}...")
                # Creating a unique request identifier to prevent duplicates
                future = executor.submit(analyze_transcript, video_id, i, part, language, api_key, analysis_url)
                futures.append((part_name, future))
            all_futures[video_id] = futures

    # Once all tasks are done, gather results and save per file
    for video_id, futures in all_futures.items():
        all_analysis_results = []
        for part_name, future in futures:
            try:
                result = future.result()
                all_analysis_results.append({part_name: result})
                logging.info(f"Completed analysis for {part_name}")
            except Exception as e:
                error_msg = f"Error processing {part_name}: {e}"
                logging.error(error_msg)
                all_analysis_results.append({part_name: {"error": error_msg}})
        
        json_output_path = os.path.join(directory, f"{video_id}_analysis.json")
        try:
            with open(json_output_path, 'w', encoding='utf-8') as json_file:
                json.dump(all_analysis_results, json_file, indent=4)
            logging.info(f"Saved analysis results: {json_output_path}")
            
            # Only update history and add to processed_set if successfully saved results
            if update_history(video_id, processed_list, history_file):
                processed_set.add(video_id)
                logging.info(f"Updated history with video_id: {video_id}")
            else:
                logging.warning(f"Failed to update history for video_id: {video_id}")
        except Exception as e:
            logging.error(f"Error writing analysis results for {video_id}: {e}")

# Main function to process all transcripts in a directory
def main(api_key, analysis_url):
    """Main function to process SRT files and analyze using Dify API."""
    # Take user inputs for directory and language
    directory_path = input("Enter the directory path containing .srt files: ").strip()
    language = input("Enter the language: ").strip()
    directory_path = os.path.abspath(directory_path)
    
    try:
        process_srt_files(directory_path, language, api_key, analysis_url)
    except Exception as e:
        logging.error(f"Error: {e}")

# Entry point of the script
if __name__ == "__main__":
    ANALYSIS_API_KEY = "app-2OH8KWvuVwXtZEgRRyejZU06"  # Replace with actual API key
    ANALYSIS_URL = "https://testing.drishtigpt.com/v1/workflows/run"  # Replace with actual API endpoint
    main(ANALYSIS_API_KEY, ANALYSIS_URL)