#!/usr/bin/env python3
"""
Script to fix encoding issues in CSV files and check content
"""

import csv
import os
import glob
import html

def fix_csv_encoding():
    """Find and fix encoding in the latest CSV file"""
    # Find all CSV files starting with 'summaries_'
    csv_files = glob.glob("summaries_*.csv")
    
    if not csv_files:
        print("❌ No CSV files found")
        return
    
    # Get the latest file
    latest_csv = max(csv_files, key=os.path.getctime)
    print(f"📄 Fixing encoding in: {latest_csv}")
    
    # Create backup
    backup_file = latest_csv.replace('.csv', '_backup.csv')
    os.rename(latest_csv, backup_file)
    print(f"💾 Backup created: {backup_file}")
    
    try:
        # Read from backup and write to new file with proper encoding
        with open(backup_file, 'r', encoding='utf-8') as input_file:
            with open(latest_csv, 'w', newline='', encoding='utf-8-sig') as output_file:
                reader = csv.DictReader(input_file)
                writer = csv.DictWriter(output_file, fieldnames=reader.fieldnames)
                writer.writeheader()
                
                count = 0
                for row in reader:
                    # Clean up the summary text
                    if 'summary_text' in row:
                        summary = row['summary_text']
                        
                        # Try to decode HTML entities if present
                        try:
                            summary = html.unescape(summary)
                        except:
                            pass
                        
                        row['summary_text'] = summary
                    
                    writer.writerow(row)
                    count += 1
                
                print(f"✅ Fixed {count} rows")
                print(f"📄 New file: {latest_csv}")
                
        # Check a sample
        print("\n🔍 Checking sample content:")
        with open(latest_csv, 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            for i, row in enumerate(reader):
                if i >= 1:  # Show first row
                    break
                    
                video_id = row.get('video_id', 'Unknown')
                summary = row.get('summary_text', '')
                
                print(f"📝 Video ID: {video_id}")
                print(f"📏 Summary length: {len(summary)} characters")
                
                # Check for proper Hindi
                if any(char in summary for char in ['क', 'ख', 'ग', 'च', 'ज', 'त', 'द', 'न', 'प', 'म']):
                    print("✅ Contains proper Hindi Devanagari characters")
                elif 'à¤' in summary:
                    print("⚠️ Still contains encoded characters")
                else:
                    print("❓ No Hindi characters detected")
                
                # Show first 200 characters
                print(f"📖 Preview: {summary[:200]}...")
                
    except Exception as e:
        print(f"❌ Error fixing encoding: {e}")
        # Restore backup if something went wrong
        if os.path.exists(backup_file):
            os.rename(backup_file, latest_csv)
            print("🔄 Restored from backup")

def check_sql_encoding():
    """Check and fix SQL file encoding"""
    sql_files = glob.glob("summaries_*.sql")
    
    if not sql_files:
        print("❌ No SQL files found")
        return
    
    latest_sql = max(sql_files, key=os.path.getctime)
    print(f"\n📄 Checking SQL file: {latest_sql}")
    
    try:
        with open(latest_sql, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for encoding issues
        if 'à¤' in content:
            print("⚠️ SQL file has encoding issues")
            
            # Create backup
            backup_sql = latest_sql.replace('.sql', '_backup.sql')
            os.rename(latest_sql, backup_sql)
            
            # Try to fix by re-reading with different encoding
            with open(backup_sql, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Write with proper encoding
            with open(latest_sql, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Fixed SQL encoding: {latest_sql}")
        else:
            print("✅ SQL file encoding looks good")
            
    except Exception as e:
        print(f"❌ Error checking SQL: {e}")

if __name__ == "__main__":
    print("🔧 Encoding Fix Tool")
    print("=" * 30)
    
    fix_csv_encoding()
    check_sql_encoding()
    
    print("\n🎉 Encoding fix completed!")
    print("💡 Tip: Future files will use UTF-8-BOM encoding for better compatibility")
