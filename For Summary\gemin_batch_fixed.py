import os
import time
import csv
import traceback
import threading
import concurrent.futures
import google.generativeai as genai
import re
import json

# ======== Configuration ========
API_KEY = "AIzaSyA2BF1yRzYQrkD_aqxk1dBjm_THcsAecLU"
MODEL_NAME = "gemini-2.5-pro-preview-03-25"
REQUESTS_PER_MIN = 5  # Based on Gemini 2.0 Pro Experimental 02-05 RPM limit
MAX_WORKERS = 5  # Process 5 files concurrently
RETRY_LIMIT = 3

# Generate a unique filename with timestamp
timestamp = time.strftime("%Y%m%d_%H%M%S")
CHECKPOINT_FILE = f"summaries_{timestamp}.csv"
CSV_HEADER = ['video_id', 'summary_text', 'language']  # Added language to header

# Base system prompt - will be customized based on language
BASE_SYSTEM_PROMPT = """You are an expert educational content summarizer specializing in creating structured, comprehensive HTML summaries from class transcripts. Your task is to transform the provided transcript into a well-organized HTML summary that follows the exact format of the sample provided.

## OUTPUT STRUCTURE
Create an HTML summary with this exact structure:
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>{title_prefix}: [Main Topic]</title>
</head>
<body>
<!-- Greeting -->
<p>{greeting}</p>
<!-- Introduction -->
<p>[Motivational introduction about the importance of this session, its relevance to exams, and benefits of understanding the content]</p>
<!-- Main Topic -->
<h1>[Main subject/topic with descriptive subtitle]</h1>
<p>[Brief overview of what was covered in the session]</p>
<!-- Session Overview -->
<h2>{overview_heading}</h2>
<ul>
  <li>[Key point 1]</li>
  <li>[Key point 2]</li>
  <!-- All major topics as bullet points -->
</ul>
<!-- Timestamp Content -->
<h2>{timestamp_heading}</h2>
<!-- For each timestamp segment -->
<p><strong>Time:</strong> [start time] – [end time]</p>
<p>[Title/description for this segment]</p>
<ul>
  <li>[Detailed point 1]</li>
  <li>[Detailed point 2]</li>
  <!-- All important information from this segment as bullet points -->
</ul>
<!-- Continue with all timestamps -->
<!-- Key Concepts -->
<h2>{concepts_heading}</h2>
<ul>
  <li><strong>[Term 1]:</strong> [Clear definition]</li>
  <li><strong>[Term 2]:</strong> [Clear definition]</li>
  <!-- All important terms with definitions -->
</ul>
<!-- Conclusion -->
<h2>{conclusion_heading}</h2>
<p>[Summary of the session and preview of the next class, including any instructions for students]</p>
</body>
</html>

## CONTENT PROCESSING GUIDELINES
1. First scan the entire transcript to identify:
   - The main topic and subtopics
   - Key timestamps where content transitions occur
   - Important terminology and concepts
   - Structure and flow of the lecture

2. Create a motivational introduction that:
   - Emphasizes the importance of the topic
   - Mentions its relevance for exams
   - Encourages students to engage with the material

3. For the session overview:
   - Create 5-8 bullet points covering the main topics
   - Keep these concise but informative
   - Ensure they represent the full range of content

4. For the timestamp segments:
   - IMPORTANT: Organize the content into NO MORE THAN 10 TIMESTAMP SECTIONS
   - Merge related segments and combine consecutive timestamps if necessary
   - Ensure each timestamp section represents a coherent, complete topic or subtopic
   - Make sure ALL major content is covered despite the consolidation
   - For each consolidated timestamp section:
     * Provide a descriptive title that captures the main theme
     * Create 4-6 detailed bullet points covering ALL significant information
     * Include all important facts, examples, case studies, and explanations
     * Ensure no important details are lost during consolidation

5. For the key concepts section:
   - Select 8-10 important terms or concepts
   - Provide clear, concise definitions
   - Focus on specialized terminology that students need to understand

6. For the conclusion:
   - Briefly summarize what was covered
   - Preview the next class topics
   - Include any instructions given to students

## CRITICAL REQUIREMENTS
1. Match the exact formatting of the sample, including:
   - The precise HTML structure
   - The style of bullet points
   - The timestamp format (00:00:00 - 00:00:00)
   - The overall organization and flow

2. Keep the same depth of detail as the sample:
   - Neither too detailed nor too sparse
   - Appropriate length for each section
   - Balance between theoretical concepts and practical applications

3. Use the specified language for the summary: {language}

4. STRICTLY ADHERE to the maximum of 10 timestamp sections while ensuring:
   - Complete coverage of the entire transcript
   - No significant content is omitted
   - All important examples, case studies and details are preserved
   - Logical grouping of related content

5. Make the summary useful for exam preparation by highlighting exam-relevant material"""

# Language-specific templates
LANGUAGE_TEMPLATES = {
    "hindi": {
        "title_prefix": "कक्षा सत्र सारांश",
        "greeting": "प्रिय विद्यार्थी, शुभकामनाएँ!",
        "overview_heading": "सत्र अवलोकन (मुख्य बिंदु)",
        "timestamp_heading": "टाइमस्टैम्प के अनुसार विषय-वस्तु",
        "concepts_heading": "प्रमुख अवधारणाएँ और शब्दावली",
        "conclusion_heading": "निष्कर्ष और अगली कक्षा",
        "language": "Hindi"
    },
    "english": {
        "title_prefix": "Class Session Summary",
        "greeting": "Dear student, best wishes!",
        "overview_heading": "Session Overview (Key Points)",
        "timestamp_heading": "Content by Timestamp",
        "concepts_heading": "Key Concepts and Terminology",
        "conclusion_heading": "Conclusion and Next Class",
        "language": "English"
    },
    "marathi": {
        "title_prefix": "वर्ग सत्र सारांश",
        "greeting": "प्रिय विद्यार्थी, शुभेच्छा!",
        "overview_heading": "सत्र आढावा (मुख्य मुद्दे)",
        "timestamp_heading": "वेळेनुसार मजकूर",
        "concepts_heading": "मुख्य संकल्पना आणि शब्दावली",
        "conclusion_heading": "निष्कर्ष आणि पुढील वर्ग",
        "language": "Marathi"
    },
    "gujarati": {
        "title_prefix": "વર્ગ સત્ર સારાંશ",
        "greeting": "પ્રિય વિદ્યાર્થી, શુભકામનાઓ!",
        "overview_heading": "સત્ર ઝાંખી (મુખ્ય મુદ્દાઓ)",
        "timestamp_heading": "સમયગાળા પ્રમાણે સામગ્રી",
        "concepts_heading": "મુખ્ય ખ્યાલો અને શબ્દાવલી",
        "conclusion_heading": "નિષ્કર્ષ અને આગલો વર્ગ",
        "language": "Gujarati"
    },
    "bengali": {
        "title_prefix": "ক্লাস সেশন সারাংশ",
        "greeting": "প্রিয় ছাত্র, শুভেচ্ছা!",
        "overview_heading": "সেশন ওভারভিউ (প্রধান পয়েন্টগুলি)",
        "timestamp_heading": "টাইমস্ট্যাম্প অনুযায়ী বিষয়বস্তু",
        "concepts_heading": "প্রধান ধারণা এবং পরিভাষা",
        "conclusion_heading": "সারসংক্ষেপ এবং পরবর্তী ক্লাস",
        "language": "Bengali"
    }
}

# ======== Rate Limiting ========
class RateLimiter:
    def __init__(self, max_requests_per_minute):
        self.max_requests = max_requests_per_minute
        self.request_timestamps = []
        self.lock = threading.Lock()
        
    def wait_if_needed(self):
        """Wait if we've reached the rate limit."""
        now = time.time()
        
        with self.lock:
            # Remove timestamps older than 1 minute
            self.request_timestamps = [ts for ts in self.request_timestamps if now - ts < 60]
            
            # If we've reached the limit, wait until we can make another request
            if len(self.request_timestamps) >= self.max_requests:
                oldest = min(self.request_timestamps)
                sleep_time = max(0, 60 - (now - oldest))
                if sleep_time > 0:
                    print(f"⏱️ Rate limit reached. Waiting {sleep_time:.1f} seconds...")
                    time.sleep(sleep_time)
            
            # Add current timestamp to the list
            self.request_timestamps.append(time.time())

# Initialize rate limiter
rate_limiter = RateLimiter(REQUESTS_PER_MIN)

# ======== HTML Cleanup ========
def clean_response(raw_text):
    raw_text = raw_text.strip()
    if raw_text.startswith("```html"):
        raw_text = raw_text[7:]
    if raw_text.endswith("```"):
        raw_text = raw_text[:-3]
    return raw_text.strip()

# ======== SRT to Text ========
def extract_transcript(srt_file):
    with open(srt_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    return ' '.join([line.strip() for line in lines if line.strip() and not line.strip().isdigit() and '-->' not in line])

# ======== Extract HTML Content ========
def extract_html_content(response):
    """Extract HTML content from the response using string methods only."""
    try:
        # Convert response to string to avoid property access issues
        response_str = str(response)
        
        # Look for HTML content - avoid attribute access altogether
        # Method 1: Look for HTML between markdown code blocks
        html_pattern = r"```html\s*(<!DOCTYPE html>.*?</html>)\s*```"
        html_match = re.search(html_pattern, response_str, re.DOTALL | re.IGNORECASE)
        if html_match:
            return html_match.group(1).strip()
        
        # Method 2: Look for direct HTML without markdown
        direct_html_pattern = r"<!DOCTYPE html>.*?</html>"
        direct_match = re.search(direct_html_pattern, response_str, re.DOTALL | re.IGNORECASE)
        if direct_match:
            return direct_match.group(0).strip()
            
        # Method 3: Try to extract from JSON representation
        # This avoids attribute access issues with protobuf
        try:
            # Find all JSON-like structures
            json_pattern = r'\{[^{}]*\{[^{}]*\}[^{}]*\}'
            matches = re.findall(json_pattern, response_str)
            
            for match in matches:
                if "text" in match and ("DOCTYPE" in match or "<html>" in match):
                    # Extract text field content
                    text_match = re.search(r'"text"\s*:\s*"(.*?)"', match, re.DOTALL)
                    if text_match:
                        # Unescape the content
                        content = text_match.group(1)
                        content = content.replace("\\n", "\n").replace('\\"', '"')
                        if "<!DOCTYPE html>" in content or "<html>" in content:
                            return content
        except:
            pass
            
        # If methods above fail, just search for any HTML-like content
        if "<!DOCTYPE html>" in response_str:
            start = response_str.find("<!DOCTYPE html>")
            end = response_str.find("</html>", start)
            if end > start:
                return response_str[start:end+7]  # +7 to include </html>
        
        # Final fallback: save the whole response as a string
        print("⚠️ Warning: Could not extract HTML, saving full response")
        return response_str
        
    except Exception as e:
        print(f"⚠️ Error extracting HTML content: {e}")
        traceback.print_exc()
        # Return something rather than None to avoid retry loops
        return f"Error extracting content: {str(e)}"

# ======== Gemini API Call ========
def summarize_transcript(transcript, language="hindi"):
    try:
        # Wait if rate limited
        rate_limiter.wait_if_needed()
        
        # Configure the API
        genai.configure(api_key=API_KEY)
        
        # Create a model instance
        model = genai.GenerativeModel(MODEL_NAME)
        
        # Get language template
        if language.lower() not in LANGUAGE_TEMPLATES:
            print(f"⚠️ Language '{language}' not supported. Defaulting to Hindi.")
            language = "hindi"
        
        template = LANGUAGE_TEMPLATES[language.lower()]
        
        # Format the system prompt with language-specific templates
        system_prompt = BASE_SYSTEM_PROMPT.format(
            title_prefix=template["title_prefix"],
            greeting=template["greeting"],
            overview_heading=template["overview_heading"],
            timestamp_heading=template["timestamp_heading"],
            concepts_heading=template["concepts_heading"],
            conclusion_heading=template["conclusion_heading"],
            language=template["language"]
        )
        
        # Prepare the message with system prompt and user content
        prompt = f"{system_prompt}\n\nTranscript to summarize:\n{transcript}"
        
        # Generate the summary
        response = model.generate_content(
            prompt,
            generation_config={
                "temperature": 0.7,
                "top_p": 0.95,
                "top_k": 64,
                "max_output_tokens": 8192,
            }
        )
        
        # Extract HTML content from the response - use simplified approach
        html_content = extract_html_content(response)
        
        return html_content
        
    except Exception as e:
        print("❌ Error summarizing transcript:", e)
        traceback.print_exc()
        return None

# ======== Process Single File ========
def process_file(file, srt_path, writer, csv_lock, language="hindi"):
    video_id = os.path.splitext(file)[0]
    print(f"\n📝 Processing: {video_id} in {language}")
    
    transcript = extract_transcript(srt_path)
    for attempt in range(RETRY_LIMIT):
        try:
            summary = summarize_transcript(transcript, language)
            if summary:
                # Decode any escaped unicode sequences in the HTML
                # This fixes Hindi text showing as \u0915\u0915\u094d\u0937\u093e etc.
                summary = summary.encode().decode('unicode_escape')
                
                with csv_lock:
                    writer.writerow([video_id, summary, language])
                print(f"✅ Saved summary for {video_id} in {language}")
                return True
            else:
                print(f"⚠️ Retry {attempt+1} failed for {video_id}")
                time.sleep(5)
        except Exception as e:
            print(f"⚠️ Attempt {attempt+1} failed for {video_id}: {e}")
            traceback.print_exc()
            time.sleep(5)
    
    print(f"❌ All retries failed for {video_id}")
    return False

# ======== Load Already Processed ========
def load_existing_checkpoints(csv_path):
    """Load IDs of already processed files, handling different CSV formats."""
    if not os.path.exists(csv_path):
        return set()
        
    with open(csv_path, newline='', encoding='utf-8') as f:
        reader = csv.reader(f)
        # Get the header row
        try:
            header = next(reader)
            
            # Try to find the column with the video ID
            id_col = 0  # Default to first column
            if 'video_id' in header:
                id_col = header.index('video_id')
                
            # Get all the IDs from that column
            return set(row[id_col] for row in reader if row)
            
        except (StopIteration, IndexError):
            # Empty file or other issue
            return set()

# ======== Main Workflow ========
def process_folder(folder_path, language="hindi"):
    # Configure the API
    genai.configure(api_key=API_KEY)
    
    srt_files = [f for f in os.listdir(folder_path) if f.endswith(".srt")]
    
    # Create checkpoint file if it doesn't exist
    output_path = os.path.join(folder_path, CHECKPOINT_FILE)
    if not os.path.exists(output_path):
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(CSV_HEADER)
    
    existing = load_existing_checkpoints(output_path)
    files_to_process = [f for f in srt_files if os.path.splitext(f)[0] not in existing]

    print(f"\n📁 Found {len(files_to_process)} new files to process in {language}.")
    
    if not files_to_process:
        print("All files have already been processed.")
        return
    
    # Using ThreadPoolExecutor to process files concurrently
    with open(output_path, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        csv_lock = threading.Lock()  # Lock for the CSV file
        
        # Create a ThreadPoolExecutor
        with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # Submit all files for processing
            futures = []
            for file in files_to_process:
                srt_path = os.path.join(folder_path, file)
                futures.append(executor.submit(process_file, file, srt_path, writer, csv_lock, language))
                
            # Wait for all futures to complete
            for future in concurrent.futures.as_completed(futures):
                future.result()  # Get the result to catch any exceptions

    print(f"\n🎉 All summaries completed in {language}!")

# ======== Run Entry Point ========
if __name__ == "__main__":
    folder = input("📂 Enter path to folder containing .srt files: ").strip()
    
    # Get language input
    print("\nAvailable languages:")
    for lang in LANGUAGE_TEMPLATES.keys():
        print(f"- {lang.capitalize()}")
    
    language = input("🌐 Enter language for summaries (default: hindi): ").strip().lower()
    if not language:
        language = "hindi"
    
    if language not in LANGUAGE_TEMPLATES:
        print(f"⚠️ Language '{language}' not supported. Defaulting to Hindi.")
        language = "hindi"
    
    if os.path.isdir(folder):
        process_folder(folder, language)
    else:
        print("❌ Invalid folder path.")