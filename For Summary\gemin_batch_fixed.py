import os
import time
import csv
import traceback
import threading
import concurrent.futures
import google.generativeai as genai
import re
import jsoncc
import argparse

# ======== Configuration ========
API_KEY = "AIzaSyA2BF1yRzYQrkD_aqxk1dBjm_THcsAecLU"
MODEL_NAME = "gemini-2.5-pro-preview-05-06"
REQUESTS_PER_MIN = 5  # Based on Gemini 2.0 Pro Experimental 02-05 RPM limit
MAX_WORKERS = 5  # Process 5 files concurrently
RETRY_LIMIT = 3

# Generate a unique filename with timestamp
timestamp = time.strftime("%Y%m%d_%H%M%S")
CHECKPOINT_FILE = f"summaries_{timestamp}.csv"
CSV_HEADER = ['video_id', 'summary_text', 'language']  # Added language to header

# English system prompt
ENGLISH_SYSTEM_PROMPT = """You have a class session transcript. Generate a comprehensive HTML summary to inspire students to watch/read the entire session.

IMPORTANT: Your response must be ONLY HTML code, no other text or explanations.

Following these precise requirements:

## 1. Technical HTML Structure
1. Begin exactly with:
   <!DOCTYPE html>
   <html>
   <head>
   <meta charset="UTF-8">
   <title>Class Session Summary: [Insert Topic Name]</title>
   </head>
   <body>
2. End exactly with:
   </body>
   </html>
3. Use only valid HTML tags: <h1>, <h2>, <h3>, <p>, <strong>, <em>, <ul>, <li>, <ol>, <div>, <span>, <br>, <hr>.
4. No CSS, JavaScript, or inline styling.
5. Create semantic document structure with appropriate heading hierarchy.

## 2. Greeting & Introduction
1. The very first lines inside the <body> **must** be:
   <p>Dear Student, Greetings!</p>
2. Immediately after, include a compelling <p> paragraph (50-75 words) explaining:
   - The session's value for exams and intellectual growth
   - How it connects to broader academic concepts
   - What specific skills/knowledge students will gain

## 3. Required Sections Structure
Create these sections in the following order, each with appropriate heading levels:
1. **Title & Brief Description**
   - <h1> for the title
   - Include subject area, topic name, and a 2-3 sentence summary

2. **Session Overview (Key Takeaways)**
   - <h2> for the heading
   - 5-7 bullet points capturing the most crucial concepts
   - Each point should be 1-2 sentences maximum

3. **Section-Wise Breakdown with Timestamps**
   - <h2> for the heading
   - IMPORTANT: Consolidate timestamps into 7-8 MAXIMUM major sections
   - Format exactly as specified below in section 5

4. **Key Concepts & Terminology**
   - <h2> for the heading
   - Each concept gets its own <h3> subheading
   - 75-100 words of explanation per concept
   - Include examples from the transcript
   - Use <strong> for key terms within explanations

5. **Practical Insights & Highlights**
   - <h2> for the heading
   - Focus on exam relevance and real-world applications
   - 3-5 bullet points with practical takeaways

6. **Conclusion & Next Class Preview**
   - <h2> for the heading
   - Summarize the session's importance (50-75 words)
   - Include preview of next class if mentioned
   - End with a motivational statement encouraging full engagement

## 4. Content Accuracy Guidelines
1. Maintain 100% factual accuracy to the transcript.
2. Correct only clear typographical errors.
3. Preserve all technical terms, proper nouns, and specialized vocabulary exactly as written.
4. Do not invent content, examples, or explanations not present in the transcript.
5. If information is ambiguous, note this with "(as mentioned in the transcript)" rather than guessing.

## 5. Timestamp Section Requirements
1. IMPORTANT: Consolidate the timestamps into 7-8 MAXIMUM major sections, even if the transcript contains many more timestamps.
2. When consolidating:
   - Prioritize major topic shifts and significant content sections
   - Merge consecutive short timestamps that cover related topics
   - Ensure the sections cover the entire lecture from beginning to end
   - Select timestamps that represent substantial chunks of content
   - Include the very first and very last timestamps to cover the full range
3. Format each timestamp section exactly as:
   <div class="timestamp-section">
     <p><strong>Time:</strong> HH:MM:SS – HH:MM:SS</p>
     <p><strong>Topic:</strong> [Major Topic Name for This Consolidated Section]</p>
     <ul>
       [4-8 detailed bullet points summarizing key content from this time segment]
     </ul>
   </div>
4. For each timestamp section:
   - Use the exact time format from the transcript (00:00:00)
   - Provide a concise, descriptive topic that captures the main theme of the section
   - Create 4-8 detailed bullet points that thoroughly explain the most important information
   - Each bullet point should be substantive (2-3 sentences) with specific examples or explanations
   - Include definitions of key terms introduced during this segment
   - Highlight significant examples, case studies, or instructor insights
   - Include any relevant sub-points or elaborations to provide comprehensive coverage

Remember: Your final summary must inspire students to engage with the full lecture material while providing a comprehensive and accurate overview of the content. Quality and accuracy are paramount."""

# Hindi system prompt
HINDI_SYSTEM_PROMPT = """आपको एक कक्षा सत्र का ट्रांसक्रिप्ट दिया गया है। कृपया एक व्यापक HTML सारांश तैयार करें जो छात्रों को पूरा सत्र देखने/पढ़ने के लिए प्रेरित करे।

महत्वपूर्ण: आपका उत्तर केवल HTML में होना चाहिए, कोई अन्य टेक्स्ट नहीं।

तकनीकी HTML संरचना:
अपना आउटपुट बिल्कुल इस प्रारूप में शुरू करें:
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>कक्षा सत्र सारांश: [विषय का नाम डालें]</title>
</head>
<body>

और बिल्कुल इस प्रकार समाप्त करें:
</body>
</html>
अनुमोदित HTML टैग:
केवल निम्नलिखित टैग का प्रयोग करें:
<h1>, <h2>, <h3>, <p>, <strong>, <em>, <ul>, <li>, <ol>, <div>, <span>, <br>, <hr>
कोई CSS, JavaScript, या इनलाइन स्टाइलिंग न जोड़ें।
HTML टैग अनुक्रम का सही उपयोग करें ताकि सारांश स्पष्ट, संरचित और पढ़ने में आसान हो।
1. अभिवादन और परिचय
<body> टैग के अंदर सबसे पहले यह पंक्ति अवश्य हो:
<p>प्रिय विद्यार्थी,
नमस्कार!</p>
इसके तुरंत बाद, एक संक्षिप्त और प्रेरणादायक परिचयात्मक अनुच्छेद (<p>, 50-75 शब्द) जिसमें शामिल हो:
सत्र की परीक्षाओं एवं बौद्धिक विकास में भूमिका।
यह अकादमिक अवधारणाओं से कैसे जुड़ा है।
छात्र कौन-से महत्वपूर्ण कौशल और ज्ञान प्राप्त करेंगे।
2. आवश्यक खंड संरचना
नीचे दिए गए सटीक क्रम में HTML शीर्षकों का उपयोग करें:
2.1. शीर्षक और संक्षिप्त विवरण
<h1> टैग के तहत:
विषय क्षेत्र, विषय का नाम, और मुख्य विषय-वस्तु का 2-3 वाक्य में सारांश।
2.2. सत्र अवलोकन (प्रमुख निष्कर्ष)
<h2> टैग के तहत:
केवल शैक्षणिक सामग्री से 6-8 प्रमुख अवधारणाओं का सार।
प्रत्येक बिंदु 1-2 वाक्य का हो, और UPSC/सरकारी परीक्षा की औपचारिक भाषा का प्रयोग हो।
3. समय अनुसार खंड-वार विभाजन (समय-निर्धारित खंड)
महत्वपूर्ण सुधार:
12-15 खंडों तक विस्तारित करें, जिससे संपूर्ण शैक्षणिक सामग्री का अधिक व्यापक कवरेज सुनिश्चित हो।
प्रत्येक समय-खंड को समेकित करें ताकि पूरा सत्र समान रूप से प्रस्तुत हो।
अनौपचारिक बातचीत, व्यक्तिगत संवाद, प्रशासनिक चर्चाएँ और अन्य गैर-शैक्षणिक सामग्री हटाएँ।
प्रत्येक समय-खंड का प्रारूप:
<div class="timestamp-section">
  <p><strong>समय:</strong> HH:MM:SS – HH:MM:SS</p>
  <p><strong>विषय:</strong> [इस खंड के लिए प्रमुख विषय]</p>
  <ul>
    [4-8 विस्तृत बुलेट पॉइंट, जो उस समय अवधि के सभी महत्वपूर्ण शैक्षणिक बिंदुओं को शामिल करें]
  </ul>
</div>
समय-खंड चयन के दिशा-निर्देश:
समय सटीक (HH:MM:SS) प्रारूप में दें।
प्रत्येक खंड 5-10 मिनट की अवधि का हो, जिससे पूरी रिकॉर्डिंग का निष्पक्ष कवरेज हो।
UPSC/सरकारी परीक्षा के तकनीकी शब्दावली और औपचारिक भाषा का प्रयोग करें।
प्रत्येक बुलेट बिंदु 2-3 वाक्य में संक्षिप्त और प्रभावी हो।
(अभिलेख में उल्लिखित अनुसार) नोट करें यदि जानकारी स्पष्ट नहीं है, कोई अनुमान न लगाएँ।
4. प्रमुख अवधारणाएँ और शब्दावली
<h2> टैग के तहत:
4-6 प्रमुख अवधारणाओं का चयन।
प्रत्येक अवधारणा का <h3> उपशीर्षक हो।
75-100 शब्दों में स्पष्ट व्याख्या दें, और महत्वपूर्ण शब्दों को <strong> में रखकर उभारें।
संस्कृत/शास्त्रीय हिंदी शब्दों को मूल रूप में बनाए रखें, आवश्यकतानुसार आधुनिक हिंदी व्याख्या दें।
5. व्यावहारिक अंतर्दृष्टि और परीक्षा प्रासंगिकता
<h2> टैग के तहत:
परीक्षा प्रासंगिकता और वास्तविक अनुप्रयोगों पर केंद्रित 3-5 बिंदु (बुलेट पॉइंट)।
UPSC/सरकारी परीक्षाओं के अनुरूप सटीकता और औपचारिकता बनाए रखें।
6. निष्कर्ष और अगली कक्षा पूर्वावलोकन
<h2> टैग के तहत:
सत्र के महत्व का 50-75 शब्दों का संक्षिप्त सारांश।
यदि अगली कक्षा का उल्लेख है, तो उसका पूर्वावलोकन दें।
प्रेरणादायक निष्कर्ष दें, जिससे छात्र संपूर्ण सामग्री से जुड़े रहें।"""

# Language-specific templates - Only English and Hindi
LANGUAGE_TEMPLATES = {
    "english": {
        "language": "English",
        "prompt": ENGLISH_SYSTEM_PROMPT
    },
    "hindi": {
        "language": "Hindi",
        "prompt": HINDI_SYSTEM_PROMPT
    }
}

# ======== Rate Limiting ========
class RateLimiter:
    def __init__(self, max_requests_per_minute):
        self.max_requests = max_requests_per_minute
        self.request_timestamps = []
        self.lock = threading.Lock()

    def wait_if_needed(self):
        """Wait if we've reached the rate limit."""
        now = time.time()

        with self.lock:
            # Remove timestamps older than 1 minute
            self.request_timestamps = [ts for ts in self.request_timestamps if now - ts < 60]

            # If we've reached the limit, wait until we can make another request
            if len(self.request_timestamps) >= self.max_requests:
                oldest = min(self.request_timestamps)
                sleep_time = max(0, 60 - (now - oldest))
                if sleep_time > 0:
                    print(f"⏱️ Rate limit reached. Waiting {sleep_time:.1f} seconds...")
                    time.sleep(sleep_time)

            # Add current timestamp to the list
            self.request_timestamps.append(time.time())

# Initialize rate limiter
rate_limiter = RateLimiter(REQUESTS_PER_MIN)

# ======== HTML Cleanup ========
def clean_response(raw_text):
    raw_text = raw_text.strip()
    if raw_text.startswith("```html"):
        raw_text = raw_text[7:]
    if raw_text.endswith("```"):
        raw_text = raw_text[:-3]
    return raw_text.strip()

# ======== SRT to Text ========
def extract_transcript(srt_file):
    with open(srt_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    return ' '.join([line.strip() for line in lines if line.strip() and not line.strip().isdigit() and '-->' not in line])

# ======== Extract HTML Content ========
def extract_html_content(response):
    """Extract HTML content from the response using improved methods."""
    try:
        # First try to access the text attribute directly
        try:
            if hasattr(response, 'text') and response.text:
                response_text = response.text
            else:
                response_text = str(response)
        except:
            response_text = str(response)

        print(f"🔍 Response length: {len(response_text)} characters")

        # Method 1: Look for HTML between markdown code blocks
        html_pattern = r"```html\s*(<!DOCTYPE html>.*?</html>)\s*```"
        html_match = re.search(html_pattern, response_text, re.DOTALL | re.IGNORECASE)
        if html_match:
            print("✅ Found HTML in markdown code blocks")
            return html_match.group(1).strip()

        # Method 2: Look for direct HTML without markdown
        direct_html_pattern = r"<!DOCTYPE html>.*?</html>"
        direct_match = re.search(direct_html_pattern, response_text, re.DOTALL | re.IGNORECASE)
        if direct_match:
            print("✅ Found direct HTML content")
            return direct_match.group(0).strip()

        # Method 3: Look for HTML starting with <html> tag
        html_tag_pattern = r"<html>.*?</html>"
        html_tag_match = re.search(html_tag_pattern, response_text, re.DOTALL | re.IGNORECASE)
        if html_tag_match:
            print("✅ Found HTML with <html> tag, adding DOCTYPE")
            html_content = html_tag_match.group(0).strip()
            # Add DOCTYPE if missing
            if not html_content.startswith("<!DOCTYPE"):
                html_content = '<!DOCTYPE html>\n' + html_content
            return html_content

        # Method 4: Check if response contains substantial content that might be HTML
        if len(response_text.strip()) > 500:
            # Look for HTML-like patterns
            if any(tag in response_text.lower() for tag in ['<h1>', '<h2>', '<p>', '<ul>', '<li>']):
                print("⚠️ Found HTML-like content without proper structure, attempting to wrap")
                # Try to wrap in basic HTML structure
                wrapped_html = f"""<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>Class Session Summary</title>
</head>
<body>
{response_text.strip()}
</body>
</html>"""
                return wrapped_html

        # If no HTML found but we have content, return it for debugging
        if len(response_text.strip()) > 50:
            print(f"⚠️ No HTML structure found. Response preview: {response_text[:200]}...")
            return response_text.strip()

        # Final fallback
        print("❌ No substantial content found in response")
        return None

    except Exception as e:
        print(f"❌ Error extracting HTML content: {e}")
        traceback.print_exc()
        return None

# ======== Extract HTML Content from Text ========
def extract_html_content_from_text(response_text):
    """Extract HTML content from plain text response."""
    try:
        print(f"🔍 Processing response: {len(response_text)} characters")

        # Method 1: Look for HTML between markdown code blocks
        html_pattern = r"```html\s*(<!DOCTYPE html>.*?</html>)\s*```"
        html_match = re.search(html_pattern, response_text, re.DOTALL | re.IGNORECASE)
        if html_match:
            print("✅ Found HTML in markdown code blocks")
            return html_match.group(1).strip()

        # Method 2: Look for direct HTML without markdown
        direct_html_pattern = r"<!DOCTYPE html>.*?</html>"
        direct_match = re.search(direct_html_pattern, response_text, re.DOTALL | re.IGNORECASE)
        if direct_match:
            print("✅ Found direct HTML content")
            return direct_match.group(0).strip()

        # Method 3: Look for HTML starting with <html> tag
        html_tag_pattern = r"<html>.*?</html>"
        html_tag_match = re.search(html_tag_pattern, response_text, re.DOTALL | re.IGNORECASE)
        if html_tag_match:
            print("✅ Found HTML with <html> tag, adding DOCTYPE")
            html_content = html_tag_match.group(0).strip()
            # Add DOCTYPE if missing
            if not html_content.startswith("<!DOCTYPE"):
                html_content = '<!DOCTYPE html>\n' + html_content
            return html_content

        # Method 4: Check if the entire response is HTML-like content
        if len(response_text.strip()) > 500:
            # Look for HTML-like patterns
            if any(tag in response_text.lower() for tag in ['<h1>', '<h2>', '<p>', '<ul>', '<li>', '<body>']):
                print("⚠️ Found HTML-like content, attempting to wrap in proper structure")
                # Check if it already has body tags
                if '<body>' in response_text.lower():
                    # Just add DOCTYPE and html wrapper if missing
                    if not response_text.strip().startswith('<!DOCTYPE'):
                        wrapped_html = f"""<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>कक्षा सत्र सारांश</title>
</head>
{response_text.strip()}
</html>"""
                        return wrapped_html
                    else:
                        return response_text.strip()
                else:
                    # Wrap in complete HTML structure
                    wrapped_html = f"""<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>कक्षा सत्र सारांश</title>
</head>
<body>
{response_text.strip()}
</body>
</html>"""
                    return wrapped_html

        # If no HTML found but we have content, return it for debugging
        if len(response_text.strip()) > 50:
            print(f"⚠️ No HTML structure found. Response preview: {response_text[:200]}...")
            return response_text.strip()

        # Final fallback
        print("❌ No substantial content found in response")
        return None

    except Exception as e:
        print(f"❌ Error extracting HTML content from text: {e}")
        traceback.print_exc()
        return None

# ======== Gemini API Call ========
def summarize_transcript(transcript, language="hindi"):
    try:
        # Wait if rate limited
        rate_limiter.wait_if_needed()

        # Configure the API
        genai.configure(api_key=API_KEY)

        # Get language template
        if language.lower() not in LANGUAGE_TEMPLATES:
            print(f"⚠️ Language '{language}' not supported. Defaulting to Hindi.")
            language = "hindi"

        template = LANGUAGE_TEMPLATES[language.lower()]
        system_prompt = template["prompt"]

        # Create a model instance with system instruction
        model = genai.GenerativeModel(
            model_name=MODEL_NAME,
            system_instruction=system_prompt
        )

        print(f"🚀 Sending request to Gemini API...")

        # Generate the summary with improved configuration
        response = model.generate_content(
            transcript,
            generation_config=genai.types.GenerationConfig(
                temperature=0.7,
                top_p=0.8,
                top_k=40,
                max_output_tokens=8192,
            )
        )

        print(f"✅ Received response!")

        # Extract text from response
        if hasattr(response, 'text') and response.text:
            response_text = response.text
            print(f"📝 Response length: {len(response_text)} characters")
        else:
            print("⚠️ No text in response, trying alternative extraction...")
            response_text = str(response)

        # Extract HTML content from the response
        html_content = extract_html_content_from_text(response_text)

        return html_content

    except Exception as e:
        print("❌ Error summarizing transcript:", e)
        traceback.print_exc()
        return None



# ======== Load Already Processed ========
def load_existing_checkpoints(csv_path):
    """Load IDs of already processed files, handling different CSV formats."""
    if not os.path.exists(csv_path):
        return set()

    with open(csv_path, newline='', encoding='utf-8') as f:
        reader = csv.reader(f)
        # Get the header row
        try:
            header = next(reader)

            # Try to find the column with the video ID
            id_col = 0  # Default to first column
            if 'video_id' in header:
                id_col = header.index('video_id')

            # Get all the IDs from that column
            return set(row[id_col] for row in reader if row)

        except (StopIteration, IndexError):
            # Empty file or other issue
            return set()

# ======== CSV to SQL Conversion ========
def escape_sql_string(s):
    """Escape single quotes in a string for SQL."""
    if s is None:
        return "NULL"
    return s.replace("'", "''")

def csv_to_sql(csv_file, sql_file, update_timestamps=True, user_id=1):
    """
    Convert CSV file to SQL INSERT statements and optionally include UPDATE statements
    for timestamp columns and user IDs.
    """
    try:
        with open(csv_file, 'r', encoding='utf-8') as csv_f:
            # Read CSV file
            reader = csv.DictReader(csv_f)

            # Store all video_ids for the timestamp update
            all_video_ids = []

            # Create SQL file
            with open(sql_file, 'w', encoding='utf-8') as sql_f:
                # Write header
                sql_f.write("-- SQL statements for importing data to dla_ai_video_summary table\n")
                sql_f.write("START TRANSACTION;\n\n")

                # Process rows in batches of 10
                batch_size = 10
                current_batch = []
                batch_counter = 0

                for row in reader:
                    if 'video_id' in row and 'summary_text' in row:
                        video_id = row['video_id'].strip()
                        summary_text = escape_sql_string(row['summary_text'])

                        # Store video_id for timestamp update
                        all_video_ids.append(video_id)

                        # Add to current batch
                        current_batch.append(f"({video_id}, '{summary_text}')")

                        # Write batch when it reaches size limit
                        if len(current_batch) >= batch_size:
                            batch_counter += 1
                            sql_f.write(f"-- Batch {batch_counter}\n")
                            sql_f.write("INSERT INTO dla_ai_video_summary (video_id, summary_text) VALUES\n")
                            sql_f.write(",\n".join(current_batch))
                            sql_f.write(";\n\n")
                            current_batch = []

                # Write any remaining rows
                if current_batch:
                    batch_counter += 1
                    sql_f.write(f"-- Batch {batch_counter}\n")
                    sql_f.write("INSERT INTO dla_ai_video_summary (video_id, summary_text) VALUES\n")
                    sql_f.write(",\n".join(current_batch))
                    sql_f.write(";\n\n")

                # Add timestamp and user ID update if requested
                if update_timestamps and all_video_ids:
                    sql_f.write("-- Update timestamp columns and user IDs\n")
                    sql_f.write(f"""
UPDATE dla_ai_video_summary
SET
  created_at = NOW(),
  updated_at = NOW(),
  created_by = {user_id},
  updated_by = {user_id}
WHERE
  video_id IN ({', '.join(all_video_ids)});
""")
                    sql_f.write("\n")

                # Commit the transaction
                sql_f.write("COMMIT;\n")

        print(f"✅ Successfully converted {csv_file} to {sql_file}")
        print(f"📊 Generated {batch_counter} INSERT batches")
        if update_timestamps:
            print(f"🕒 Added timestamp UPDATE statement for {len(all_video_ids)} rows")
        return True

    except Exception as e:
        print(f"❌ Error converting CSV to SQL: {e}")
        traceback.print_exc()
        return False

# ======== Enhanced Retry Logic ========
def process_file_with_enhanced_retry(file, srt_path, writer, csv_lock, language="hindi", max_retries=5):
    """Enhanced version of process_file with better retry logic and failure tracking."""
    video_id = os.path.splitext(file)[0]
    print(f"\n📝 Processing: {video_id} in {language}")

    transcript = extract_transcript(srt_path)

    for attempt in range(max_retries):
        try:
            print(f"🔄 Attempt {attempt + 1}/{max_retries} for {video_id}")
            summary = summarize_transcript(transcript, language)

            if summary and len(summary.strip()) > 200:  # Ensure we have substantial content
                # Decode any escaped unicode sequences in the HTML
                try:
                    # Try the standard unicode_escape decoding
                    summary = summary.encode().decode('unicode_escape')
                except UnicodeDecodeError as ude:
                    # Handle the case where there's a trailing backslash
                    print(f"⚠️ Unicode decode error: {ude}")
                    if "\\ at end of string" in str(ude):
                        # Add a space after any trailing backslash to make it decodable
                        modified_summary = summary
                        if modified_summary.endswith('\\'):
                            modified_summary += ' '
                        try:
                            summary = modified_summary.encode().decode('unicode_escape')
                            print("✅ Fixed trailing backslash issue")
                        except UnicodeDecodeError:
                            print("⚠️ Could not fix unicode decoding, using original string")
                    else:
                        print("⚠️ Skipping unicode_escape decoding due to errors")

                # Validate that we have proper HTML structure (more flexible)
                has_html_structure = (
                    ("<!DOCTYPE html>" in summary or "<html>" in summary) and
                    "</html>" in summary and
                    any(tag in summary for tag in ["<h1>", "<h2>", "<p>"])
                )

                if has_html_structure:
                    with csv_lock:
                        writer.writerow([video_id, summary, language])
                    print(f"✅ Saved summary for {video_id} in {language}")
                    return True
                else:
                    print(f"⚠️ Invalid HTML structure in attempt {attempt + 1} for {video_id}")
                    print(f"   Summary preview: {summary[:100]}...")
                    # Save debug response for analysis
                    if attempt == max_retries - 1:  # Last attempt
                        save_debug_response(video_id, summary, os.path.dirname(srt_path))
            else:
                print(f"⚠️ Empty or insufficient summary in attempt {attempt + 1} for {video_id}")
                if summary:
                    print(f"   Response length: {len(summary)} characters")
                    # Save debug response for analysis
                    if attempt == max_retries - 1:  # Last attempt
                        save_debug_response(video_id, summary, os.path.dirname(srt_path))

            # Wait before retry with exponential backoff
            wait_time = min(30, 5 * (2 ** attempt))  # Cap at 30 seconds
            print(f"⏳ Waiting {wait_time} seconds before retry...")
            time.sleep(wait_time)

        except Exception as e:
            print(f"⚠️ Attempt {attempt + 1} failed for {video_id}: {e}")
            traceback.print_exc()

            # Wait before retry with exponential backoff
            wait_time = min(30, 5 * (2 ** attempt))
            print(f"⏳ Waiting {wait_time} seconds before retry...")
            time.sleep(wait_time)

    print(f"❌ All {max_retries} retries failed for {video_id}")

    # Log failed file for manual review
    failed_log_path = os.path.join(os.path.dirname(srt_path), "failed_summaries.txt")
    with open(failed_log_path, 'a', encoding='utf-8') as f:
        f.write(f"{video_id}\t{language}\t{time.strftime('%Y-%m-%d %H:%M:%S')}\n")

    return False

# ======== Debug Function ========
def save_debug_response(video_id, response_text, folder_path):
    """Save problematic responses for debugging."""
    debug_folder = os.path.join(folder_path, "debug_responses")
    os.makedirs(debug_folder, exist_ok=True)

    debug_file = os.path.join(debug_folder, f"{video_id}_response.txt")
    with open(debug_file, 'w', encoding='utf-8') as f:
        f.write(f"Video ID: {video_id}\n")
        f.write(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Response Length: {len(response_text)}\n")
        f.write("="*50 + "\n")
        f.write(response_text)

    print(f"🐛 Debug response saved: {debug_file}")

# ======== Main Workflow ========
def process_folder(folder_path, language="hindi"):
    # Configure the API
    genai.configure(api_key=API_KEY)

    srt_files = [f for f in os.listdir(folder_path) if f.endswith(".srt")]

    # Create checkpoint file if it doesn't exist
    output_path = os.path.join(folder_path, CHECKPOINT_FILE)
    if not os.path.exists(output_path):
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(CSV_HEADER)

    existing = load_existing_checkpoints(output_path)
    files_to_process = [f for f in srt_files if os.path.splitext(f)[0] not in existing]

    print(f"\n📁 Found {len(files_to_process)} new files to process in {language}.")

    if not files_to_process:
        print("All files have already been processed.")
        return

    # Using ThreadPoolExecutor to process files concurrently
    with open(output_path, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        csv_lock = threading.Lock()  # Lock for the CSV file

        # Create a ThreadPoolExecutor
        with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # Submit all files for processing using enhanced retry function
            futures = []
            for file in files_to_process:
                srt_path = os.path.join(folder_path, file)
                futures.append(executor.submit(process_file_with_enhanced_retry, file, srt_path, writer, csv_lock, language))

            # Wait for all futures to complete and track results
            successful = 0
            failed = 0
            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result()
                    if result:
                        successful += 1
                    else:
                        failed += 1
                except Exception as e:
                    print(f"❌ Unexpected error in processing: {e}")
                    failed += 1

    print(f"\n🎉 Processing completed in {language}!")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")

    # Offer to convert CSV to SQL
    if successful > 0:
        convert_choice = input("\n🔄 Would you like to convert the CSV to SQL format? (y/n): ").strip().lower()
        if convert_choice in ['y', 'yes']:
            sql_filename = output_path.replace('.csv', '.sql')
            if csv_to_sql(output_path, sql_filename):
                print(f"📄 SQL file created: {sql_filename}")
            else:
                print("❌ Failed to create SQL file")

# ======== Run Entry Point ========
if __name__ == "__main__":
    print("🎓 AI Video Summary Generator")
    print("=" * 50)

    folder = input("📂 Enter path to folder containing .srt files: ").strip()

    # Get language input - only English and Hindi
    print("\n🌐 Available languages:")
    print("1. Hindi (हिंदी)")
    print("2. English")

    while True:
        choice = input("\n🔤 Select language (1 for Hindi, 2 for English, default: Hindi): ").strip()

        if choice == "2" or choice.lower() in ["english", "eng", "e"]:
            language = "english"
            break
        elif choice == "1" or choice.lower() in ["hindi", "hin", "h"] or choice == "":
            language = "hindi"
            break
        else:
            print("⚠️ Invalid choice. Please enter 1 for Hindi or 2 for English.")

    print(f"✅ Selected language: {LANGUAGE_TEMPLATES[language]['language']}")

    if os.path.isdir(folder):
        print(f"\n🚀 Starting processing with enhanced retry logic...")
        process_folder(folder, language)
    else:
        print("❌ Invalid folder path. Please check the path and try again.")